# encoding: utf-8
"""
ChargeProcessor – portable GUI
Startet den (eingefrorenen) sap_orchestrator.exe aus demselben Ordner.
"""
import locale
import os
import re
import subprocess
import sys
import threading
import time
import tkinter as tk
from pathlib import Path
from tkinter import filedialog, messagebox, ttk
from typing import Dict, Optional

# --------------------------------------------------------------------------
# Hilfsfunktionen für portable Pfade
# --------------------------------------------------------------------------
def get_base_dir() -> Path:
    """
    Rückgabe des Ordners, in dem die EXE bzw. das Skript liegt.
    """
    if getattr(sys, "frozen", False):
        return Path(sys.executable).parent
    return Path(__file__).parent


BASE_DIR = get_base_dir()

# --------------------------------------------------------------------------
# Settings-Manager
# --------------------------------------------------------------------------
class SettingsManager:
    """Verwaltet die Konfigurationsparameter in sap_orchestrator.py"""

    def __init__(self):
        self.orchestrator_path = BASE_DIR / "sap_orchestrator.py"

    def read_settings(self) -> Dict[str, str]:
        """Liest die aktuellen Einstellungen aus sap_orchestrator.py"""
        settings = {}
        if not self.orchestrator_path.exists():
            return settings

        try:
            with open(self.orchestrator_path, "r", encoding="utf-8") as f:
                content = f.read()

            # Regex-Patterns für die verschiedenen Konstanten
            patterns = {
                "SAP_EXECUTABLE_PATH": r'SAP_EXECUTABLE_PATH\s*=\s*r?"([^"]*)"',
                "SAP_SYSTEM_ID": r'SAP_SYSTEM_ID\s*=\s*"([^"]*)"',
                "SAP_CLIENT": r'SAP_CLIENT\s*=\s*"([^"]*)"',
                "SAP_LANGUAGE": r'SAP_LANGUAGE\s*=\s*"([^"]*)"',
                "LAGERORT_VON": r'LAGERORT_VON\s*=\s*"([^"]*)"',
                "LAGERORT_NACH": r'LAGERORT_NACH\s*=\s*"([^"]*)"',
                "LAGERTYP": r'LAGERTYP\s*=\s*"([^"]*)"',
                "LAGERNUMMER_VON": r'LAGERNUMMER_VON\s*=\s*"([^"]*)"',
                "LAGERNUMMER_NACH": r'LAGERNUMMER_NACH\s*=\s*"([^"]*)"',
                "WERK": r'WERK\s*=\s*"([^"]*)"',
                "EXPORT_FILE_NAME": r'EXPORT_FILE_NAME\s*=\s*"([^"]*)"',
                "EXPORT_FILE_PATH": r'EXPORT_FILE_PATH\s*=\s*r?"([^"]*)"',
            }

            for key, pattern in patterns.items():
                match = re.search(pattern, content)
                settings[key] = match.group(1) if match else ""

        except Exception as e:
            messagebox.showerror(
                "Fehler", f"Fehler beim Lesen der Einstellungen: {e}"
            )

        return settings

    def write_settings(self, settings: Dict[str, str]) -> bool:
        """Schreibt die Einstellungen zurück in sap_orchestrator.py"""
        if not self.orchestrator_path.exists():
            messagebox.showerror(
                "Fehler", f"Datei {self.orchestrator_path} nicht gefunden"
            )
            return False

        try:
            with open(self.orchestrator_path, "r", encoding="utf-8") as f:
                content = f.read()

            # SAP_EXECUTABLE_PATH (Raw-String)
            sap_path = settings.get("SAP_EXECUTABLE_PATH", "")
            pattern = r'SAP_EXECUTABLE_PATH\s*=\s*r?"[^"]*"'
            content = re.sub(
                pattern,
                lambda m: f'SAP_EXECUTABLE_PATH = r"{sap_path}"',
                content,
            )

            # EXPORT_FILE_PATH (Raw-String)
            export_path = settings.get("EXPORT_FILE_PATH", "")
            pattern = r'EXPORT_FILE_PATH\s*=\s*r?"[^"]*"'
            content = re.sub(
                pattern,
                lambda m: f'EXPORT_FILE_PATH = r"{export_path}"',
                content,
            )

            # Normale String-Konstanten
            normal_settings = [
                "SAP_SYSTEM_ID",
                "SAP_CLIENT",
                "SAP_LANGUAGE",
                "LAGERORT_VON",
                "LAGERORT_NACH",
                "LAGERTYP",
                "LAGERNUMMER_VON",
                "LAGERNUMMER_NACH",
                "WERK",
                "EXPORT_FILE_NAME",
            ]

            for key in normal_settings:
                value = settings.get(key, "")
                pattern = key + r'\s*=\s*"[^"]*"'
                content = re.sub(
                    pattern,
                    lambda m, k=key, v=value: f'{k} = "{v}"',
                    content,
                )

            with open(self.orchestrator_path, "w", encoding="utf-8") as f:
                f.write(content)

            return True

        except Exception as e:
            messagebox.showerror(
                "Fehler", f"Fehler beim Schreiben der Einstellungen: {e}"
            )
            return False

# --------------------------------------------------------------------------
# Settings-Dialog
# --------------------------------------------------------------------------
class SettingsDialog:
    """Dialog zum Bearbeiten der SAP-Konfiguration"""

    def __init__(self, parent):
        self.parent = parent
        self.settings_manager = SettingsManager()
        self.result = None

        # Dialog-Fenster erstellen
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("SAP Einstellungen")
        self.dialog.geometry("600x500")
        self.dialog.resizable(True, True)
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # Zentrieren
        self.dialog.geometry("+%d+%d" % (parent.winfo_rootx() + 50, parent.winfo_rooty() + 50))

        self.create_widgets()
        self.load_settings()

    def create_widgets(self):
        """Erstellt die GUI-Elemente"""
        main_frame = ttk.Frame(self.dialog, padding=10)
        main_frame.pack(fill="both", expand=True)

        # Scrollable Frame
        canvas = tk.Canvas(main_frame)
        scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda _: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # Entry-Felder für alle Einstellungen
        self.entries = {}

        # SAP System Einstellungen
        ttk.Label(scrollable_frame, text="SAP System Einstellungen", font=("TkDefaultFont", 10, "bold")).grid(
            row=0, column=0, columnspan=2, sticky="w", pady=(0, 10)
        )

        row = 1
        settings_config = [
            ("SAP_EXECUTABLE_PATH", "SAP Executable Pfad:", True),
            ("SAP_SYSTEM_ID", "System ID:", False),
            ("SAP_CLIENT", "Client:", False),
            ("SAP_LANGUAGE", "Sprache:", False),
        ]

        for key, label, has_browse in settings_config:
            ttk.Label(scrollable_frame, text=label).grid(row=row, column=0, sticky="w", pady=2)

            if has_browse:
                frame = ttk.Frame(scrollable_frame)
                frame.grid(row=row, column=1, sticky="we", padx=(10, 0), pady=2)

                entry = ttk.Entry(frame, width=50)
                entry.pack(side="left", fill="x", expand=True)

                browse_btn = ttk.Button(frame, text="...", width=3,
                                      command=lambda k=key: self.browse_file(k))
                browse_btn.pack(side="right", padx=(5, 0))

                self.entries[key] = entry
            else:
                entry = ttk.Entry(scrollable_frame, width=50)
                entry.grid(row=row, column=1, sticky="we", padx=(10, 0), pady=2)
                self.entries[key] = entry

            row += 1

        # Lager Einstellungen
        ttk.Label(scrollable_frame, text="Lager Einstellungen", font=("TkDefaultFont", 10, "bold")).grid(
            row=row, column=0, columnspan=2, sticky="w", pady=(20, 10)
        )
        row += 1

        lager_settings = [
            ("LAGERORT_VON", "Lagerort Von:"),
            ("LAGERORT_NACH", "Lagerort Nach:"),
            ("LAGERTYP", "Lagertyp:"),
            ("LAGERNUMMER_VON", "Lagernummer Von:"),
            ("LAGERNUMMER_NACH", "Lagernummer Nach:"),
            ("WERK", "Werk:"),
        ]

        for key, label in lager_settings:
            ttk.Label(scrollable_frame, text=label).grid(row=row, column=0, sticky="w", pady=2)
            entry = ttk.Entry(scrollable_frame, width=50)
            entry.grid(row=row, column=1, sticky="we", padx=(10, 0), pady=2)
            self.entries[key] = entry
            row += 1

        # Export Einstellungen
        ttk.Label(scrollable_frame, text="Export Einstellungen", font=("TkDefaultFont", 10, "bold")).grid(
            row=row, column=0, columnspan=2, sticky="w", pady=(20, 10)
        )
        row += 1

        export_settings = [
            ("EXPORT_FILE_NAME", "Export Dateiname:"),
            ("EXPORT_FILE_PATH", "Export Pfad:"),
        ]

        for key, label in export_settings:
            ttk.Label(scrollable_frame, text=label).grid(row=row, column=0, sticky="w", pady=2)

            if key == "EXPORT_FILE_PATH":
                frame = ttk.Frame(scrollable_frame)
                frame.grid(row=row, column=1, sticky="we", padx=(10, 0), pady=2)

                entry = ttk.Entry(frame, width=50)
                entry.pack(side="left", fill="x", expand=True)

                browse_btn = ttk.Button(frame, text="...", width=3,
                                      command=lambda k=key: self.browse_folder(k))
                browse_btn.pack(side="right", padx=(5, 0))

                self.entries[key] = entry
            else:
                entry = ttk.Entry(scrollable_frame, width=50)
                entry.grid(row=row, column=1, sticky="we", padx=(10, 0), pady=2)
                self.entries[key] = entry

            row += 1

        # Scrollable Frame konfigurieren
        scrollable_frame.columnconfigure(1, weight=1)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Button Frame
        button_frame = ttk.Frame(self.dialog)
        button_frame.pack(fill="x", padx=10, pady=10)

        ttk.Button(button_frame, text="Speichern", command=self.save_settings).pack(side="right", padx=(5, 0))
        ttk.Button(button_frame, text="Abbrechen", command=self.cancel).pack(side="right")

    def browse_file(self, key):
        """Öffnet einen Datei-Dialog"""
        filename = filedialog.askopenfilename(
            title="SAP Executable auswählen",
            filetypes=[("Executable files", "*.exe"), ("All files", "*.*")]
        )
        if filename:
            self.entries[key].delete(0, tk.END)
            self.entries[key].insert(0, filename)

    def browse_folder(self, key):
        """Öffnet einen Ordner-Dialog"""
        folder = filedialog.askdirectory(title="Export-Ordner auswählen")
        if folder:
            self.entries[key].delete(0, tk.END)
            self.entries[key].insert(0, folder)

    def load_settings(self):
        """Lädt die aktuellen Einstellungen"""
        settings = self.settings_manager.read_settings()
        for key, entry in self.entries.items():
            value = settings.get(key, "")
            entry.delete(0, tk.END)
            entry.insert(0, value)

    def save_settings(self):
        """Speichert die Einstellungen"""
        settings = {}
        for key, entry in self.entries.items():
            settings[key] = entry.get().strip()

        if self.settings_manager.write_settings(settings):
            messagebox.showinfo("Erfolg", "Einstellungen wurden gespeichert.")
            self.result = True
            self.dialog.destroy()

    def cancel(self):
        """Bricht den Dialog ab"""
        self.result = False
        self.dialog.destroy()

# --------------------------------------------------------------------------
# Hintergrund-Worker
# --------------------------------------------------------------------------
class BackgroundProcess:
    def __init__(
        self,
        charge: str,
        on_progress,
        on_done,
        on_error,
        on_log,
        extra_flag: Optional[str] = None,
    ):
        self.charge = charge
        self.on_progress = on_progress
        self.on_done = on_done
        self.on_error = on_error
        self.on_log = on_log
        self.extra_flag = extra_flag
        self._thread: Optional[threading.Thread] = None
        self._cancelled = threading.Event()

    # -------------------------
    def start(self) -> None:
        self._thread = threading.Thread(target=self._run, daemon=True)
        self._thread.start()

    def cancel(self) -> None:
        self._cancelled.set()

    # -------------------------
    def _run(self) -> None:
        try:
            # Arbeitsverzeichnis auf portable Ordner setzen
            os.chdir(BASE_DIR)

            # Pfad zur Orchestrator-EXE bzw. .py ermitteln
            exe_path = BASE_DIR / "sap_orchestrator.exe"
            if exe_path.exists():
                cmd = [str(exe_path), "--charge", self.charge]
            else:
                script_path = BASE_DIR / "sap_orchestrator.py"
                if not script_path.exists():
                    raise FileNotFoundError(
                        f"sap_orchestrator.exe/.py nicht gefunden in {BASE_DIR}"
                    )
                cmd = [sys.executable, str(script_path), "--charge", self.charge]

            if self.extra_flag:
                cmd.append(self.extra_flag)

            self.on_log(f"Starte: {' '.join(cmd)}\n")

            proc = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                encoding=locale.getpreferredencoding(False),
                errors="replace",
            )

            start_time = time.time()
            timeout = 300  # s
            while True:
                if self._cancelled.is_set():
                    proc.terminate()
                    self.on_error(RuntimeError("Prozess abgebrochen"))
                    return

                if time.time() - start_time > timeout:
                    proc.terminate()
                    self.on_error(RuntimeError("Timeout erreicht"))
                    return

                line = proc.stdout.readline()
                if not line and proc.poll() is not None:
                    break

                if line:
                    self.on_log(line)
                    if line.startswith("PROGRESS "):
                        parts = line.split(" ", 2)
                        percent = int(parts[1]) if len(parts) > 1 else None
                        msg = parts[2] if len(parts) > 2 else ""
                        self.on_progress(percent, msg)
                        start_time = time.time()
                else:
                    time.sleep(0.1)

            rc = proc.wait()
            if rc == 0:
                self.on_done("Prozess beendet")
            else:
                self.on_error(RuntimeError(f"Exit-Code {rc}"))

        except Exception as exc:
            self.on_error(exc)


# --------------------------------------------------------------------------
# Tk-GUI
# --------------------------------------------------------------------------
class App(tk.Tk):
    def __init__(self) -> None:
        super().__init__()
        self.title("Charge Processor")
        self.geometry("420x300")
        self.resizable(False, False)

        self._worker: Optional[BackgroundProcess] = None
        self._indeterminate = False

        # Menü erstellen
        self.create_menu()

        main = ttk.Frame(self, padding=16)
        main.pack(fill="both", expand=True)

        # Eingabe Charge
        ttk.Label(main, text="Charge").grid(row=0, column=0, sticky="w")
        self.var_charge = tk.StringVar()
        ttk.Entry(main, textvariable=self.var_charge, width=36).grid(
            row=1, column=0, columnspan=2, sticky="we", pady=(0, 8)
        )

        # Trockenlauf
        self.var_dry = tk.BooleanVar()
        ttk.Checkbutton(main, text="Trockenlauf (ohne SAP)", variable=self.var_dry).grid(
            row=2, column=0, columnspan=2, sticky="w", pady=(0, 8)
        )

        # Buttons
        ttk.Button(main, text="Start", command=self.on_start).grid(
            row=3, column=0, sticky="we"
        )
        ttk.Button(main, text="Abbrechen", command=self.on_cancel).grid(
            row=3, column=1, sticky="we"
        )

        # Progress + Status
        self.progress = ttk.Progressbar(main, orient="horizontal", mode="determinate")
        self.progress.grid(row=4, column=0, columnspan=2, sticky="we", pady=(8, 0))

        self.var_status = tk.StringVar()
        ttk.Label(main, textvariable=self.var_status).grid(
            row=5, column=0, columnspan=2, sticky="w"
        )

        # Log
        self.log = tk.Text(main, height=6, width=50)
        self.log.grid(row=6, column=0, columnspan=2, sticky="we", pady=(8, 0))
        scroll = ttk.Scrollbar(main, orient="vertical", command=self.log.yview)
        scroll.grid(row=6, column=2, sticky="ns")
        self.log["yscrollcommand"] = scroll.set

        main.columnconfigure(0, weight=1)
        main.columnconfigure(1, weight=1)

        self.bind("<Return>", lambda _: self.on_start())

    def create_menu(self):
        """Erstellt das Hauptmenü"""
        menubar = tk.Menu(self)
        self.config(menu=menubar)

        # Datei-Menü
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Datei", menu=file_menu)
        file_menu.add_command(label="Einstellungen...", command=self.open_settings)
        file_menu.add_separator()
        file_menu.add_command(label="Beenden", command=self.quit)

        # Hilfe-Menü
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Hilfe", menu=help_menu)
        help_menu.add_command(label="Über...", command=self.show_about)

    def open_settings(self):
        """Öffnet den Settings-Dialog"""
        dialog = SettingsDialog(self)
        self.wait_window(dialog.dialog)

    def show_about(self):
        """Zeigt den Über-Dialog"""
        messagebox.showinfo(
            "Über Charge Processor",
            "Charge Processor v1.0\n\n"
            "SAP GUI Automatisierung für Umlagerungsprozesse\n\n"
            "Entwickelt für die Verwaltung von Chargen und Lagerorten."
        )

    # ----------------- GUI-Events -----------------------------------------
    def on_start(self) -> None:
        charge = self.var_charge.get().strip()
        if not charge:
            messagebox.showwarning("Eingabe fehlt", "Bitte eine Charge eingeben.")
            return

        self._set_running(True)
        self.progress["value"] = 0
        self.var_status.set("Starte ...")
        self.log.delete(1.0, tk.END)

        self._worker = BackgroundProcess(
            charge,
            self._on_progress,
            self._on_done,
            self._on_error,
            self._on_log,
            extra_flag="--dry-run" if self.var_dry.get() else None,
        )
        self._worker.start()

    def on_cancel(self) -> None:
        if self._worker:
            self._worker.cancel()

    # ---------------- Worker-Callbacks ------------------------------------
    def _on_progress(self, percent: Optional[int], msg: str) -> None:
        if percent is None:
            if not self._indeterminate:
                self.progress.configure(mode="indeterminate")
                self.progress.start(10)
                self._indeterminate = True
        else:
            if self._indeterminate:
                self.progress.stop()
                self.progress.configure(mode="determinate")
                self._indeterminate = False
            self.progress["value"] = percent
            self.var_status.set(f"{percent}% – {msg}")

    def _on_done(self, msg: str) -> None:
        self.progress.stop()
        self.progress["value"] = 100
        self.var_status.set(msg)
        self.after(1000, lambda: self._set_running(False))

    def _on_error(self, exc: Exception) -> None:
        self.progress.stop()
        self.progress["value"] = 0
        self._set_running(False)
        messagebox.showerror("Fehler", str(exc))

    def _on_log(self, line: str) -> None:
        self.log.insert(tk.END, line)
        self.log.see(tk.END)

    # ---------------- intern ----------------------------------------------
    def _set_running(self, running: bool) -> None:
        state = "disabled" if running else "normal"
        for child in self.children.values():
            if isinstance(child, ttk.Entry) or isinstance(child, ttk.Button):
                child["state"] = state
        self.progress.configure(mode="determinate")
        self._indeterminate = False


def main() -> None:
    App().mainloop()


if __name__ == "__main__":
    main()