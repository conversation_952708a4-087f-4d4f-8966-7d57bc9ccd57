import os
import subprocess
import time
import win32com.client
import pandas as pd
import sqlite3
from datetime import datetime

# --- KONFIGURATION ---
SAP_EXECUTABLE_PATH = r"C:\Program Files (x86)\SAP\FrontEnd\SapGui\sapshcut.exe"
SAP_SYSTEM_ID = "PS4"
SAP_CLIENT = "009"
SAP_LANGUAGE = "DE"
SCRIPT_DATE = datetime.now()
DB_PATH = "sap_datenbank.db"

# -- Konfiguration für den SG Prozess --
FIRST_TCODE = "/n/LSGIT/VS_DLV_CHECK"
FIRST_EXPORT_DIR = r"\\adsgroup\Group\UIL-CL-Zentral\10 Dashboard-App\SourceData\SG"
FIRST_EXPORT_BASENAME = "SG"
FIRST_DB_TABLE = "delivery_checks"

# -- Konfiguration für den RSL Prozess --
SECOND_TCODE = "/n/LSGIT/VS_DLV_CHECK"
SECOND_EXPORT_DIR = r"\\adsgroup\Group\UIL-CL-Zentral\10 Dashboard-App\SourceData\RSL"
SECOND_EXPORT_BASENAME = "RSL"
SECOND_DB_TABLE = "rsl_variant_data"

# -- Konfiguration für den BST 240 Prozess --
THIRD_TCODE = "/nlx03"
THIRD_EXPORT_DIR = r"\\adsgroup\Group\UIL-CL-Zentral\10 Dashboard-App\SourceData\BST"
THIRD_EXPORT_BASENAME = "BST_240"
THIRD_DB_TABLE = "lx03_bin_status"

# -- Konfiguration für den BST 240 Prozess --
FOURTH_TCODE = "/nlx03"
FOURTH_EXPORT_DIR = r"\\adsgroup\Group\UIL-CL-Zentral\10 Dashboard-App\SourceData\BST"
FOURTH_EXPORT_BASENAME = "BST_200"
FOURTH_DB_TABLE = "lx03_bin_status"

# -- Konfiguration für den BST REST Prozess --
FIFTH_TCODE = "/nlx03"
FIFTH_EXPORT_DIR = r"\\adsgroup\Group\UIL-CL-Zentral\10 Dashboard-App\SourceData\BST"
FIFTH_EXPORT_BASENAME = "BST_REST"
FIFTH_DB_TABLE = "lx03_bin_status"
# --- ENDE DER KONFIGURATION ---


def close_excel():
    """Schließt alle laufenden Excel-Prozesse robust mit taskkill."""
    print("Versuche, alle Excel-Prozesse zu schließen...")
    try:
        subprocess.run(['taskkill', '/F', '/IM', 'excel.exe'], check=True, capture_output=True, text=True)
        print("Excel wurde erfolgreich geschlossen.")
    except subprocess.CalledProcessError as e:
        if "not found" in e.stderr or "Der Prozess" in e.stderr:
            print("Excel war nicht geöffnet, nichts zu tun.")
        else:
            print(f"Ein Fehler ist beim Schließen von Excel aufgetreten: {e.stderr}")
    except FileNotFoundError:
        print("Fehler: Der Befehl 'taskkill' wurde nicht gefunden.")


def wait_for_element(session, element_id, timeout=30):
    """Wartet aktiv darauf, dass ein UI-Element in der SAP-Sitzung erscheint."""
    print(f"Warte auf Element mit ID: {element_id}...")
    start_time = time.time()
    while time.time() - start_time < timeout:
        try:
            element = session.findById(element_id)
            print("Element gefunden.")
            return element
        except Exception:
            time.sleep(0.5)
    raise TimeoutError(f"Zeitüberschreitung: Element '{element_id}' wurde nach {timeout} Sekunden nicht gefunden.")


def get_sap_session():
    """Stellt eine Verbindung zu einer SAP-Sitzung her oder startet SAP."""
    try:
        sap_gui_auto = win32com.client.GetObject("SAPGUI")
        application = sap_gui_auto.GetScriptingEngine
        connection = application.Children(0)
        session = connection.Children(0)
        print("Bestehende SAP GUI-Instanz gefunden.")
    except Exception:
        print("SAP GUI nicht gefunden. Starte SAP über sapshcut.exe...")
        command = f'"{SAP_EXECUTABLE_PATH}" -system="{SAP_SYSTEM_ID}" -client="{SAP_CLIENT}" -language="{SAP_LANGUAGE}" -maxgui'
        subprocess.Popen(command)
        print("Warte 15 Sekunden, bis SAP gestartet ist...")
        time.sleep(15)
        sap_gui_auto = win32com.client.GetObject("SAPGUI")
        application = sap_gui_auto.GetScriptingEngine
        connection = application.Children(0)
        session = connection.Children(0)
    
    wait_for_element(session, "wnd[0]")
    print("SAP-Sitzung erfolgreich verbunden und bereit.")
    return session


def run_first_process(session, script_date):
    """Führt den Servicegrad SAP-Prozess aus."""
    try:
        print("Führe das Servicegrad SAP-Automatisierungsskript aus...")
        formatted_date = script_date.strftime("%d.%m.%Y")
        timestamp_str = datetime.now().strftime("%d%m%Y_%H%M%S")
        export_full_filename = f"{FIRST_EXPORT_BASENAME}_{timestamp_str}.xlsx"
        
        print(f"Starte Transaktion: {FIRST_TCODE}...")
        session.findById("wnd[0]").maximize()
        session.findById("wnd[0]/tbar[0]/okcd").text = FIRST_TCODE
        session.findById("wnd[0]").sendVKey(0)
        
        date_field = wait_for_element(session, "wnd[0]/usr/ctxtS_LDDAT-LOW")
        date_field.text = formatted_date
        date_field.setFocus()
        
        wait_for_element(session, "wnd[0]/tbar[1]/btn[8]").press()
        wait_for_element(session, "wnd[0]/tbar[1]/btn[33]").press()
        
        layout_grid = wait_for_element(session, "wnd[1]/usr/subSUB_CONFIGURATION:SAPLSALV_CUL_LAYOUT_CHOOSE:0500/cntlD500_CONTAINER/shellcont/shell")
        layout_grid.setCurrentCell(2, "TEXT")
        layout_grid.selectedRows = "2"
        layout_grid.clickCurrentCell()
        
        wait_for_element(session, "wnd[0]").findById("mbar/menu[0]/menu[3]/menu[1]").select()
        
        export_config_field = wait_for_element(session, "wnd[1]/usr/ssubSUB_CONFIGURATION:SAPLSALV_GUI_CUL_EXPORT_AS:0512/txtGS_EXPORT-FILE_NAME")
        export_config_field.text = FIRST_EXPORT_BASENAME
        session.findById("wnd[1]/tbar[0]/btn[20]").press()
        
        print("Pause von 2 Sekunden, um auf den finalen Speichern-Dialog zu warten...")
        time.sleep(2)
        
        path_field = wait_for_element(session, "wnd[1]/usr/ctxtDY_PATH")
        os.makedirs(FIRST_EXPORT_DIR, exist_ok=True)
        path_field.text = FIRST_EXPORT_DIR
        session.findById("wnd[1]/usr/ctxtDY_FILENAME").text = export_full_filename
        session.findById("wnd[1]/tbar[0]/btn[0]").press()

        full_export_path = os.path.join(FIRST_EXPORT_DIR, export_full_filename)
        print(f"Datei '{full_export_path}' wird exportiert. Warte auf Dateisystem...")
        time.sleep(5)
        
        print("Servicegrad Prozess abgeschlossen.")
        return full_export_path
    except Exception as e:
        print(f"Ein Fehler während des ERSTEN Prozesses ist aufgetreten: {e}")
        return None

def run_second_process(session):
    """Führt den Rückstandsliste SAP-Prozess aus."""
    try:
        print("Führe das ZWEITE SAP-Automatisierungsskript aus...")
        timestamp_str = datetime.now().strftime("%d%m%Y_%H%M%S")
        export_full_filename = f"{SECOND_EXPORT_BASENAME}_{timestamp_str}.xlsx"

        session.findById("wnd[0]/tbar[0]/btn[3]").press()
        time.sleep(1)
        session.findById("wnd[0]/tbar[0]/btn[3]").press()
        time.sleep(2)

        print(f"Starte Transaktion: {SECOND_TCODE}...")
        session.findById("wnd[0]/tbar[0]/okcd").text = SECOND_TCODE
        session.findById("wnd[0]").sendVKey(0)

        wait_for_element(session, "wnd[0]/tbar[1]/btn[17]").press()
        time.sleep(2)

        variant_user_field = wait_for_element(session, "wnd[1]/usr/txtENAME-LOW")
        variant_user_field.text = ""
        session.findById("wnd[1]/tbar[0]/btn[8]").press()
        time.sleep(2)

        variant_list = wait_for_element(session, "wnd[1]/usr/cntlALV_CONTAINER_1/shellcont/shell")
        variant_list.currentCellRow = 20
        variant_list.firstVisibleRow = 3
        variant_list.selectedRows = "20"
        variant_list.doubleClickCurrentCell()
        time.sleep(2)

        wait_for_element(session, "wnd[0]/tbar[1]/btn[8]").press()

        wait_for_element(session, "wnd[0]/tbar[1]/btn[33]").press()
        time.sleep(2)

        layout_grid = wait_for_element(session, "wnd[1]/usr/subSUB_CONFIGURATION:SAPLSALV_CUL_LAYOUT_CHOOSE:0500/cntlD500_CONTAINER/shellcont/shell")
        layout_grid.setCurrentCell(7, "TEXT")
        layout_grid.selectedRows = "7"
        layout_grid.clickCurrentCell()
        time.sleep(2)

        wait_for_element(session, "wnd[0]").findById("mbar/menu[0]/menu[3]/menu[1]").select()
        time.sleep(2)

        export_config_field = wait_for_element(session, "wnd[1]/usr/ssubSUB_CONFIGURATION:SAPLSALV_GUI_CUL_EXPORT_AS:0512/txtGS_EXPORT-FILE_NAME")
        export_config_field.text = SECOND_EXPORT_BASENAME
        session.findById("wnd[1]/tbar[0]/btn[20]").press()

        print("Pause von 2 Sekunden, um auf den finalen Speichern-Dialog zu warten...")
        time.sleep(2)
        
        path_field = wait_for_element(session, "wnd[1]/usr/ctxtDY_PATH")
        os.makedirs(SECOND_EXPORT_DIR, exist_ok=True)
        path_field.text = SECOND_EXPORT_DIR
        session.findById("wnd[1]/usr/ctxtDY_FILENAME").text = export_full_filename
        session.findById("wnd[1]/tbar[0]/btn[0]").press()

        full_export_path = os.path.join(SECOND_EXPORT_DIR, export_full_filename)
        print(f"Datei '{full_export_path}' wird exportiert. Warte auf Dateisystem...")
        time.sleep(5)
        
        print("Rückstandsliste Prozess abgeschlossen.")
        return full_export_path
    except Exception as e:
        print(f"Ein Fehler während des ZWEITEN Prozesses ist aufgetreten: {e}")
        return None

def run_third_process(session):
    """Führt den dritten SAP-Prozess aus."""
    try:
        print("Führe das LX03 Lagertyp 240 SAP-Automatisierungsskript aus...")
        timestamp_str = datetime.now().strftime("%d%m%Y_%H%M%S")
        export_full_filename = f"{THIRD_EXPORT_BASENAME}_{timestamp_str}.xlsx"

        session.findById("wnd[0]/tbar[0]/btn[3]").press()
        time.sleep(1)
        session.findById("wnd[0]/tbar[0]/btn[3]").press()
        time.sleep(2)

        print(f"Starte Transaktion: {THIRD_TCODE}...")
        session.findById("wnd[0]/tbar[0]/okcd").text = THIRD_TCODE
        session.findById("wnd[0]").sendVKey(0)
        time.sleep(2) # Pause nach Start der Transaktion

        # Felder ausfüllen
        wait_for_element(session, "wnd[0]/usr/chkPMITB").selected = True
        wait_for_element(session, "wnd[0]/usr/ctxtS1_LGNUM").text = "512"
        wait_for_element(session, "wnd[0]/usr/ctxtS1_LGTYP-LOW").text = "240"
        
        # Bericht ausführen
        wait_for_element(session, "wnd[0]/tbar[1]/btn[8]").press()
        time.sleep(2) # Pause nach Ausführen

        # Layout auswählen
        wait_for_element(session, "wnd[0]/tbar[1]/btn[33]").press()
        time.sleep(2) # Pause für Layout-Fenster

        label = wait_for_element(session, "wnd[1]/usr/lbl[14,5]")
        label.setFocus()
        session.findById("wnd[1]").sendVKey(2) # "Auswählen"
        time.sleep(2) # Pause nach Auswahl

        # Export
        wait_for_element(session, "wnd[0]").findById("mbar/menu[0]/menu[1]/menu[1]").select()
        time.sleep(2) # Pause für Export-Fenster

        export_config_field = wait_for_element(session, "wnd[1]/usr/ssubSUB_CONFIGURATION:SAPLSALV_GUI_CUL_EXPORT_AS:0512/txtGS_EXPORT-FILE_NAME")
        export_config_field.text = THIRD_EXPORT_BASENAME
        session.findById("wnd[1]/tbar[0]/btn[20]").press()
        time.sleep(2)

        path_field = wait_for_element(session, "wnd[1]/usr/ctxtDY_PATH")
        os.makedirs(THIRD_EXPORT_DIR, exist_ok=True)
        path_field.text = THIRD_EXPORT_DIR
        session.findById("wnd[1]/usr/ctxtDY_FILENAME").text = export_full_filename
        session.findById("wnd[1]/tbar[0]/btn[0]").press()

        full_export_path = os.path.join(THIRD_EXPORT_DIR, export_full_filename)
        print(f"Datei '{full_export_path}' wird exportiert. Warte auf Dateisystem...")
        time.sleep(5)
        
        print("Dritter Prozess abgeschlossen.")
        return full_export_path
    except Exception as e:
        print(f"Ein Fehler während des DRITTEN Prozesses ist aufgetreten: {e}")
        return None


def write_excel_to_sqlite(excel_path, db_path, table_name):
    """Liest eine Excel-Datei und schreibt sie in eine SQLite-Datenbank."""
    if not excel_path or not os.path.exists(excel_path):
        print(f"Fehler: Die Excel-Datei '{excel_path}' wurde nicht gefunden.")
        return False
    try:
        print(f"Lese Daten aus '{excel_path}'...")
        df = pd.read_excel(excel_path, engine='openpyxl')
        conn = sqlite3.connect(db_path)
        df.to_sql(table_name, conn, if_exists='replace', index=False)
        conn.close()
        print(f"Daten erfolgreich in Tabelle '{table_name}' geschrieben.")
        return True
    except Exception as e:
        print(f"Ein Fehler beim Verarbeiten der Datei ist aufgetreten: {e}")
        return False

def run_fourth_process(session):
    """Führt den LX03 Lagertyp 200 SAP-Prozess aus."""
    try:
        print("Führe das LX03 mit Lagertyp 200 SAP-Automatisierungsskript aus...")
        timestamp_str = datetime.now().strftime("%d%m%Y_%H%M%S")
        export_full_filename = f"{FOURTH_EXPORT_BASENAME}_{timestamp_str}.xlsx"

        session.findById("wnd[0]/tbar[0]/btn[3]").press()
        time.sleep(1)
        session.findById("wnd[0]/tbar[0]/btn[3]").press()
        time.sleep(2)

        print(f"Starte Transaktion: {FOURTH_TCODE}...")
        session.findById("wnd[0]/tbar[0]/okcd").text = FOURTH_TCODE
        session.findById("wnd[0]").sendVKey(0)
        time.sleep(2) # Pause nach Start der Transaktion

        # Felder ausfüllen
        wait_for_element(session, "wnd[0]/usr/chkPMITB").selected = True
        wait_for_element(session, "wnd[0]/usr/ctxtS1_LGNUM").text = "512"
        wait_for_element(session, "wnd[0]/usr/ctxtS1_LGTYP-LOW").text = "200"
        
        # Bericht ausführen
        wait_for_element(session, "wnd[0]/tbar[1]/btn[8]").press()
        time.sleep(2) # Pause nach Ausführen

        # Layout auswählen
        wait_for_element(session, "wnd[0]/tbar[1]/btn[33]").press()
        time.sleep(2) # Pause für Layout-Fenster

        label = wait_for_element(session, "wnd[1]/usr/lbl[14,5]")
        label.setFocus()
        session.findById("wnd[1]").sendVKey(2) # "Auswählen"
        time.sleep(2) # Pause nach Auswahl

        # Export
        wait_for_element(session, "wnd[0]").findById("mbar/menu[0]/menu[1]/menu[1]").select()
        time.sleep(2) # Pause für Export-Fenster

        export_config_field = wait_for_element(session, "wnd[1]/usr/ssubSUB_CONFIGURATION:SAPLSALV_GUI_CUL_EXPORT_AS:0512/txtGS_EXPORT-FILE_NAME")
        export_config_field.text = FOURTH_EXPORT_BASENAME
        session.findById("wnd[1]/tbar[0]/btn[20]").press()
        time.sleep(2)

        path_field = wait_for_element(session, "wnd[1]/usr/ctxtDY_PATH")
        os.makedirs(FOURTH_EXPORT_DIR, exist_ok=True)
        path_field.text = FOURTH_EXPORT_DIR
        session.findById("wnd[1]/usr/ctxtDY_FILENAME").text = export_full_filename
        session.findById("wnd[1]/tbar[0]/btn[0]").press()

        full_export_path = os.path.join(FOURTH_EXPORT_DIR, export_full_filename)
        print(f"Datei '{full_export_path}' wird exportiert. Warte auf Dateisystem...")
        time.sleep(5)
        
        print("LX03 Lagertyp 200 Prozess abgeschlossen.")
        return full_export_path
    except Exception as e:
        print(f"Ein Fehler während des DRITTEN Prozesses ist aufgetreten: {e}")
        return None


def write_excel_to_sqlite(excel_path, db_path, table_name):
    """Liest eine Excel-Datei und schreibt sie in eine SQLite-Datenbank."""
    if not excel_path or not os.path.exists(excel_path):
        print(f"Fehler: Die Excel-Datei '{excel_path}' wurde nicht gefunden.")
        return False
    try:
        print(f"Lese Daten aus '{excel_path}'...")
        df = pd.read_excel(excel_path, engine='openpyxl')
        conn = sqlite3.connect(db_path)
        df.to_sql(table_name, conn, if_exists='replace', index=False)
        conn.close()
        print(f"Daten erfolgreich in Tabelle '{table_name}' geschrieben.")
        return True
    except Exception as e:
        print(f"Ein Fehler beim Verarbeiten der Datei ist aufgetreten: {e}")
        return False
    
def run_fifth_process(session):
    """Führt den LX03 Lagertyp rest SAP-Prozess aus."""
    try:
        print("Führe das LX03 mit Lagertyp rest SAP-Automatisierungsskript aus...")
        timestamp_str = datetime.now().strftime("%d%m%Y_%H%M%S")
        export_full_filename = f"{FIFTH_EXPORT_BASENAME}_{timestamp_str}.xlsx"

        session.findById("wnd[0]/tbar[0]/btn[3]").press()
        time.sleep(1)
        session.findById("wnd[0]/tbar[0]/btn[3]").press()
        time.sleep(2)

        print(f"Starte Transaktion: {FIFTH_TCODE}...")
        session.findById("wnd[0]/tbar[0]/okcd").text = FIFTH_TCODE
        session.findById("wnd[0]").sendVKey(0)
        time.sleep(2) # Pause nach Start der Transaktion

        # Felder ausfüllen
        wait_for_element(session, "wnd[0]/usr/chkPMITB").selected = True
        wait_for_element(session, "wnd[0]/usr/ctxtS1_LGNUM").text = "512"
        wait_for_element(session, "wnd[0]/usr/ctxtS1_LGTYP-LOW").text = "241"
        wait_for_element(session, "wnd[0]/usr/ctxtS1_LGTYP-HIGH").text = "999"
        
        # Bericht ausführen
        wait_for_element(session, "wnd[0]/tbar[1]/btn[8]").press()
        time.sleep(2) # Pause nach Ausführen

        # Layout auswählen
        wait_for_element(session, "wnd[0]/tbar[1]/btn[33]").press()
        time.sleep(2) # Pause für Layout-Fenster

        label = wait_for_element(session, "wnd[1]/usr/lbl[14,5]")
        label.setFocus()
        session.findById("wnd[1]").sendVKey(2) # "Auswählen"
        time.sleep(2) # Pause nach Auswahl

        # Export
        wait_for_element(session, "wnd[0]").findById("mbar/menu[0]/menu[1]/menu[1]").select()
        time.sleep(2) # Pause für Export-Fenster

        export_config_field = wait_for_element(session, "wnd[1]/usr/ssubSUB_CONFIGURATION:SAPLSALV_GUI_CUL_EXPORT_AS:0512/txtGS_EXPORT-FILE_NAME")
        export_config_field.text = FIFTH_EXPORT_BASENAME
        session.findById("wnd[1]/tbar[0]/btn[20]").press()
        time.sleep(2)

        path_field = wait_for_element(session, "wnd[1]/usr/ctxtDY_PATH")
        os.makedirs(FOURTH_EXPORT_DIR, exist_ok=True)
        path_field.text = FOURTH_EXPORT_DIR
        session.findById("wnd[1]/usr/ctxtDY_FILENAME").text = export_full_filename
        session.findById("wnd[1]/tbar[0]/btn[0]").press()

        full_export_path = os.path.join(FIFTH_EXPORT_DIR, export_full_filename)
        print(f"Datei '{full_export_path}' wird exportiert. Warte auf Dateisystem...")
        time.sleep(5)
        
        print("LX03 Lagertyp rest Prozess abgeschlossen.")
        return full_export_path
    except Exception as e:
        print(f"Ein Fehler während des DRITTEN Prozesses ist aufgetreten: {e}")
        return None


def write_excel_to_sqlite(excel_path, db_path, table_name):
    """Liest eine Excel-Datei und schreibt sie in eine SQLite-Datenbank."""
    if not excel_path or not os.path.exists(excel_path):
        print(f"Fehler: Die Excel-Datei '{excel_path}' wurde nicht gefunden.")
        return False
    try:
        print(f"Lese Daten aus '{excel_path}'...")
        df = pd.read_excel(excel_path, engine='openpyxl')
        conn = sqlite3.connect(db_path)
        df.to_sql(table_name, conn, if_exists='replace', index=False)
        conn.close()
        print(f"Daten erfolgreich in Tabelle '{table_name}' geschrieben.")
        return True
    except Exception as e:
        print(f"Ein Fehler beim Verarbeiten der Datei ist aufgetreten: {e}")
        return False
    
if __name__ == "__main__":
    try:
        sap_session = get_sap_session()
        if sap_session:
            # --- SERVICEGRAD PROZESS AUSFÜHREN ---
            print("\n" + "="*20 + " STARTE PROZESS SERVICEGRAD " + "="*20)
            first_exported_file = run_first_process(sap_session, SCRIPT_DATE)
            if first_exported_file:
                success1 = write_excel_to_sqlite(first_exported_file, DB_PATH, FIRST_DB_TABLE)
                if success1:
                    print("Warte 3 Sekunden, um sicherzustellen, dass Excel bereit ist...")
                    time.sleep(3)
                    close_excel()
            
            # --- RÜCKSTANDSLISTE PROZESS AUSFÜHREN ---
            print("\n" + "="*20 + " STARTE PROZESS RÜCKSTANDSLISTE " + "="*20)
            second_exported_file = run_second_process(sap_session)
            if second_exported_file:
                success2 = write_excel_to_sqlite(second_exported_file, DB_PATH, SECOND_DB_TABLE)
                if success2:
                    print("Warte 3 Sekunden, um sicherzustellen, dass Excel bereit ist...")
                    time.sleep(3)
                    close_excel()

            # --- LX03 LAGERTYP 240 PROZESS AUSFÜHREN ---
            print("\n" + "="*20 + " STARTE PROZESS LX03 LAGERTYP 240 " + "="*20)
            third_exported_file = run_third_process(sap_session)
            if third_exported_file:
                success3 = write_excel_to_sqlite(third_exported_file, DB_PATH, THIRD_DB_TABLE)
                if success3:
                    print("Warte 3 Sekunden, um sicherzustellen, dass Excel bereit ist...")
                    time.sleep(3)
                    close_excel()

            # --- LX03 LAGERTYP 200 PROZESS AUSFÜHREN ---
            print("\n" + "="*20 + " STARTE LX03 LAGERTYP 200 " + "="*20)
            third_exported_file = run_third_process(sap_session)
            if third_exported_file:
                success3 = write_excel_to_sqlite(third_exported_file, DB_PATH, FOURTH_DB_TABLE)
                if success3:
                    print("Warte 3 Sekunden, um sicherzustellen, dass Excel bereit ist...")
                    time.sleep(3)
                    close_excel()

            # --- LX03 LAGERTYP REST PROZESS AUSFÜHREN ---
            print("\n" + "="*20 + " STARTE LX03 LAGERTYP REST " + "="*20)
            third_exported_file = run_third_process(sap_session)
            if third_exported_file:
                success3 = write_excel_to_sqlite(third_exported_file, DB_PATH, FIFTH_DB_TABLE)
                if success3:
                    print("Warte 3 Sekunden, um sicherzustellen, dass Excel bereit ist...")
                    time.sleep(3)
                    close_excel()

            print("\nAlle Prozesse erfolgreich abgeschlossen!")

    except Exception as e:
        print(f"Ein kritischer Fehler ist im Hauptprozess aufgetreten: {e}")