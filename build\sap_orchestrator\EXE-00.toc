('C:\\my\\umlagerung\\dist\\sap_orchestrator.exe',
 True,
 False,
 False,
 'C:\\Users\\<USER>\\OneDrive - '
 'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\PyInstaller\\bootloader\\images\\icon-console.ico',
 None,
 False,
 False,
 b'<?xml version="1.0" encoding="UTF-8" standalone="yes"?>\n<assembly xmlns='
 b'"urn:schemas-microsoft-com:asm.v1" manifestVersion="1.0">\n  <trustInfo x'
 b'mlns="urn:schemas-microsoft-com:asm.v3">\n    <security>\n      <requested'
 b'Privileges>\n        <requestedExecutionLevel level="asInvoker" uiAccess='
 b'"false"/>\n      </requestedPrivileges>\n    </security>\n  </trustInfo>\n  '
 b'<compatibility xmlns="urn:schemas-microsoft-com:compatibility.v1">\n    <'
 b'application>\n      <supportedOS Id="{e2011457-1546-43c5-a5fe-008deee3d3f'
 b'0}"/>\n      <supportedOS Id="{35138b9a-5d96-4fbd-8e2d-a2440225f93a}"/>\n '
 b'     <supportedOS Id="{4a2f28e3-53b9-4441-ba9c-d69d4a4a6e38}"/>\n      <s'
 b'upportedOS Id="{1f676c76-80e1-4239-95bb-83d0f6d0da78}"/>\n      <supporte'
 b'dOS Id="{8e0f7a12-bfb3-4fe8-b9a5-48fd50a15a9a}"/>\n    </application>\n  <'
 b'/compatibility>\n  <application xmlns="urn:schemas-microsoft-com:asm.v3">'
 b'\n    <windowsSettings>\n      <longPathAware xmlns="http://schemas.micros'
 b'oft.com/SMI/2016/WindowsSettings">true</longPathAware>\n    </windowsSett'
 b'ings>\n  </application>\n  <dependency>\n    <dependentAssembly>\n      <ass'
 b'emblyIdentity type="win32" name="Microsoft.Windows.Common-Controls" version='
 b'"6.0.0.0" processorArchitecture="*" publicKeyToken="6595b64144ccf1df" langua'
 b'ge="*"/>\n    </dependentAssembly>\n  </dependency>\n</assembly>',
 True,
 False,
 None,
 None,
 None,
 'C:\\my\\umlagerung\\build\\sap_orchestrator\\sap_orchestrator.pkg',
 [('pyi-runtime-tmpdir .', '', 'OPTION'),
  ('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'C:\\my\\umlagerung\\build\\sap_orchestrator\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'C:\\my\\umlagerung\\build\\sap_orchestrator\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'C:\\my\\umlagerung\\build\\sap_orchestrator\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'C:\\my\\umlagerung\\build\\sap_orchestrator\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'C:\\my\\umlagerung\\build\\sap_orchestrator\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'C:\\my\\umlagerung\\build\\sap_orchestrator\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('pyi_rth_pywintypes',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_pywintypes.py',
   'PYSOURCE'),
  ('pyi_rth_pythoncom',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_pythoncom.py',
   'PYSOURCE'),
  ('sap_orchestrator', 'C:\\my\\umlagerung\\sap_orchestrator.py', 'PYSOURCE'),
  ('python312.dll',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\python312.dll',
   'BINARY'),
  ('numpy.libs\\libopenblas64__v0.3.23-293-gc2f4bdbb-gcc_10_3_0-2bde3a66a51006b2b53eb373ff767a3f.dll',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\numpy.libs\\libopenblas64__v0.3.23-293-gc2f4bdbb-gcc_10_3_0-2bde3a66a51006b2b53eb373ff767a3f.dll',
   'BINARY'),
  ('pywintypes312.dll',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\pywintypes312.dll',
   'BINARY'),
  ('pythoncom312.dll',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\pythoncom312.dll',
   'BINARY'),
  ('unicodedata.pyd',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('select.pyd',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_queue.pyd',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\DLLs\\_queue.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('_ssl.pyd',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\DLLs\\_ssl.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_wmi.pyd',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\DLLs\\_wmi.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('_multiprocessing.pyd',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('numpy\\core\\_multiarray_tests.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\numpy\\core\\_multiarray_tests.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\core\\_multiarray_umath.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\numpy\\core\\_multiarray_umath.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('psutil\\_psutil_windows.pyd',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\psutil\\_psutil_windows.pyd',
   'EXTENSION'),
  ('win32\\win32pdh.pyd',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32\\win32pdh.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\mtrand.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\numpy\\random\\mtrand.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\numpy\\random\\_sfc64.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\numpy\\random\\_philox.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\numpy\\random\\_pcg64.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\numpy\\random\\_mt19937.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\numpy\\random\\bit_generator.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\numpy\\random\\_generator.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_bounded_integers.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\numpy\\random\\_bounded_integers.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\numpy\\random\\_common.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_internal.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\numpy\\fft\\_pocketfft_internal.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('yaml\\_yaml.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\yaml\\_yaml.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('Pythonwin\\win32ui.pyd',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\Pythonwin\\win32ui.pyd',
   'EXTENSION'),
  ('win32\\win32event.pyd',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32\\win32event.pyd',
   'EXTENSION'),
  ('win32\\win32clipboard.pyd',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32\\win32clipboard.pyd',
   'EXTENSION'),
  ('win32com\\taskscheduler\\taskscheduler.pyd',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32comext\\taskscheduler\\taskscheduler.pyd',
   'EXTENSION'),
  ('win32com\\shell\\shell.pyd',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32comext\\shell\\shell.pyd',
   'EXTENSION'),
  ('win32\\winxpgui.pyd',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32\\winxpgui.pyd',
   'EXTENSION'),
  ('win32\\win32trace.pyd',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32\\win32trace.pyd',
   'EXTENSION'),
  ('win32com\\propsys\\propsys.pyd',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32comext\\propsys\\propsys.pyd',
   'EXTENSION'),
  ('win32com\\mapi\\mapi.pyd',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32comext\\mapi\\mapi.pyd',
   'EXTENSION'),
  ('win32com\\mapi\\exchange.pyd',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32comext\\mapi\\exchange.pyd',
   'EXTENSION'),
  ('win32com\\internet\\internet.pyd',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32comext\\internet\\internet.pyd',
   'EXTENSION'),
  ('win32com\\ifilter\\ifilter.pyd',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32comext\\ifilter\\ifilter.pyd',
   'EXTENSION'),
  ('win32com\\directsound\\directsound.pyd',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32comext\\directsound\\directsound.pyd',
   'EXTENSION'),
  ('win32com\\bits\\bits.pyd',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32comext\\bits\\bits.pyd',
   'EXTENSION'),
  ('win32com\\axscript\\axscript.pyd',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32comext\\axscript\\axscript.pyd',
   'EXTENSION'),
  ('win32com\\axcontrol\\axcontrol.pyd',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32comext\\axcontrol\\axcontrol.pyd',
   'EXTENSION'),
  ('win32com\\authorization\\authorization.pyd',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32comext\\authorization\\authorization.pyd',
   'EXTENSION'),
  ('win32com\\adsi\\adsi.pyd',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32comext\\adsi\\adsi.pyd',
   'EXTENSION'),
  ('win32\\win32service.pyd',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32\\win32service.pyd',
   'EXTENSION'),
  ('win32\\win32security.pyd',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32\\win32security.pyd',
   'EXTENSION'),
  ('win32\\win32process.pyd',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32\\win32process.pyd',
   'EXTENSION'),
  ('win32\\win32gui.pyd',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32\\win32gui.pyd',
   'EXTENSION'),
  ('win32\\win32api.pyd',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32\\win32api.pyd',
   'EXTENSION'),
  ('win32\\_win32sysloader.pyd',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32\\_win32sysloader.pyd',
   'EXTENSION'),
  ('VCRUNTIME140.dll',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\VCRUNTIME140.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('libcrypto-3.dll',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\DLLs\\libcrypto-3.dll',
   'BINARY'),
  ('libffi-8.dll',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\DLLs\\libffi-8.dll',
   'BINARY'),
  ('libssl-3.dll',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\DLLs\\libssl-3.dll',
   'BINARY'),
  ('python3.dll',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\python3.dll',
   'BINARY'),
  ('Pythonwin\\mfc140u.dll',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\Pythonwin\\mfc140u.dll',
   'BINARY'),
  ('app.py', 'C:\\my\\umlagerung\\app.py', 'DATA'),
  ('debug.py', 'C:\\my\\umlagerung\\debug.py', 'DATA'),
  ('pywin32-306.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\pywin32-306.dist-info\\INSTALLER',
   'DATA'),
  ('pywin32-306.dist-info\\METADATA',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\pywin32-306.dist-info\\METADATA',
   'DATA'),
  ('pywin32-306.dist-info\\RECORD',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\pywin32-306.dist-info\\RECORD',
   'DATA'),
  ('pywin32-306.dist-info\\REQUESTED',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\pywin32-306.dist-info\\REQUESTED',
   'DATA'),
  ('pywin32-306.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\pywin32-306.dist-info\\WHEEL',
   'DATA'),
  ('pywin32-306.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\pywin32-306.dist-info\\top_level.txt',
   'DATA'),
  ('sap_orchestrator.py', 'C:\\my\\umlagerung\\sap_orchestrator.py', 'DATA'),
  ('win32com\\HTML\\GeneratedSupport.html',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\HTML\\GeneratedSupport.html',
   'DATA'),
  ('win32com\\HTML\\PythonCOM.html',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\HTML\\PythonCOM.html',
   'DATA'),
  ('win32com\\HTML\\QuickStartClientCom.html',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\HTML\\QuickStartClientCom.html',
   'DATA'),
  ('win32com\\HTML\\QuickStartServerCom.html',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\HTML\\QuickStartServerCom.html',
   'DATA'),
  ('win32com\\HTML\\docindex.html',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\HTML\\docindex.html',
   'DATA'),
  ('win32com\\HTML\\image\\BTN_HomePage.gif',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\HTML\\image\\BTN_HomePage.gif',
   'DATA'),
  ('win32com\\HTML\\image\\BTN_ManualTop.gif',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\HTML\\image\\BTN_ManualTop.gif',
   'DATA'),
  ('win32com\\HTML\\image\\BTN_NextPage.gif',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\HTML\\image\\BTN_NextPage.gif',
   'DATA'),
  ('win32com\\HTML\\image\\BTN_PrevPage.gif',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\HTML\\image\\BTN_PrevPage.gif',
   'DATA'),
  ('win32com\\HTML\\image\\blank.gif',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\HTML\\image\\blank.gif',
   'DATA'),
  ('win32com\\HTML\\image\\pycom_blowing.gif',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\HTML\\image\\pycom_blowing.gif',
   'DATA'),
  ('win32com\\HTML\\image\\pythoncom.gif',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\HTML\\image\\pythoncom.gif',
   'DATA'),
  ('win32com\\HTML\\image\\www_icon.gif',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\HTML\\image\\www_icon.gif',
   'DATA'),
  ('win32com\\HTML\\index.html',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\HTML\\index.html',
   'DATA'),
  ('win32com\\HTML\\misc.html',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\HTML\\misc.html',
   'DATA'),
  ('win32com\\HTML\\package.html',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\HTML\\package.html',
   'DATA'),
  ('win32com\\HTML\\variant.html',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\HTML\\variant.html',
   'DATA'),
  ('win32com\\License.txt',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\License.txt',
   'DATA'),
  ('win32com\\__init__.py',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\__init__.py',
   'DATA'),
  ('win32com\\client\\CLSIDToClass.py',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\client\\CLSIDToClass.py',
   'DATA'),
  ('win32com\\client\\__init__.py',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\client\\__init__.py',
   'DATA'),
  ('win32com\\client\\build.py',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\client\\build.py',
   'DATA'),
  ('win32com\\client\\combrowse.py',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\client\\combrowse.py',
   'DATA'),
  ('win32com\\client\\connect.py',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\client\\connect.py',
   'DATA'),
  ('win32com\\client\\dynamic.py',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\client\\dynamic.py',
   'DATA'),
  ('win32com\\client\\gencache.py',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\client\\gencache.py',
   'DATA'),
  ('win32com\\client\\genpy.py',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\client\\genpy.py',
   'DATA'),
  ('win32com\\client\\makepy.py',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\client\\makepy.py',
   'DATA'),
  ('win32com\\client\\selecttlb.py',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\client\\selecttlb.py',
   'DATA'),
  ('win32com\\client\\tlbrowse.py',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\client\\tlbrowse.py',
   'DATA'),
  ('win32com\\client\\util.py',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\client\\util.py',
   'DATA'),
  ('win32com\\demos\\__init__.py',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\demos\\__init__.py',
   'DATA'),
  ('win32com\\demos\\connect.py',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\demos\\connect.py',
   'DATA'),
  ('win32com\\demos\\dump_clipboard.py',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\demos\\dump_clipboard.py',
   'DATA'),
  ('win32com\\demos\\eventsApartmentThreaded.py',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\demos\\eventsApartmentThreaded.py',
   'DATA'),
  ('win32com\\demos\\eventsFreeThreaded.py',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\demos\\eventsFreeThreaded.py',
   'DATA'),
  ('win32com\\demos\\excelAddin.py',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\demos\\excelAddin.py',
   'DATA'),
  ('win32com\\demos\\excelRTDServer.py',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\demos\\excelRTDServer.py',
   'DATA'),
  ('win32com\\demos\\iebutton.py',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\demos\\iebutton.py',
   'DATA'),
  ('win32com\\demos\\ietoolbar.py',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\demos\\ietoolbar.py',
   'DATA'),
  ('win32com\\demos\\outlookAddin.py',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\demos\\outlookAddin.py',
   'DATA'),
  ('win32com\\demos\\trybag.py',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\demos\\trybag.py',
   'DATA'),
  ('win32com\\include\\PythonCOM.h',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\include\\PythonCOM.h',
   'DATA'),
  ('win32com\\include\\PythonCOMRegister.h',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\include\\PythonCOMRegister.h',
   'DATA'),
  ('win32com\\include\\PythonCOMServer.h',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\include\\PythonCOMServer.h',
   'DATA'),
  ('win32com\\libs\\axscript.lib',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\libs\\axscript.lib',
   'DATA'),
  ('win32com\\libs\\pythoncom.lib',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\libs\\pythoncom.lib',
   'DATA'),
  ('win32com\\makegw\\__init__.py',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\makegw\\__init__.py',
   'DATA'),
  ('win32com\\makegw\\makegw.py',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\makegw\\makegw.py',
   'DATA'),
  ('win32com\\makegw\\makegwenum.py',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\makegw\\makegwenum.py',
   'DATA'),
  ('win32com\\makegw\\makegwparse.py',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\makegw\\makegwparse.py',
   'DATA'),
  ('win32com\\olectl.py',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\olectl.py',
   'DATA'),
  ('win32com\\readme.html',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\readme.html',
   'DATA'),
  ('win32com\\server\\__init__.py',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\server\\__init__.py',
   'DATA'),
  ('win32com\\server\\connect.py',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\server\\connect.py',
   'DATA'),
  ('win32com\\server\\dispatcher.py',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\server\\dispatcher.py',
   'DATA'),
  ('win32com\\server\\exception.py',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\server\\exception.py',
   'DATA'),
  ('win32com\\server\\factory.py',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\server\\factory.py',
   'DATA'),
  ('win32com\\server\\localserver.py',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\server\\localserver.py',
   'DATA'),
  ('win32com\\server\\policy.py',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\server\\policy.py',
   'DATA'),
  ('win32com\\server\\register.py',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\server\\register.py',
   'DATA'),
  ('win32com\\server\\util.py',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\server\\util.py',
   'DATA'),
  ('win32com\\servers\\PythonTools.py',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\servers\\PythonTools.py',
   'DATA'),
  ('win32com\\servers\\__init__.py',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\servers\\__init__.py',
   'DATA'),
  ('win32com\\servers\\dictionary.py',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\servers\\dictionary.py',
   'DATA'),
  ('win32com\\servers\\interp.py',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\servers\\interp.py',
   'DATA'),
  ('win32com\\servers\\perfmon.py',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\servers\\perfmon.py',
   'DATA'),
  ('win32com\\servers\\test_pycomtest.py',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\servers\\test_pycomtest.py',
   'DATA'),
  ('win32com\\storagecon.py',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\storagecon.py',
   'DATA'),
  ('win32com\\test\\GenTestScripts.py',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\test\\GenTestScripts.py',
   'DATA'),
  ('win32com\\test\\Testpys.sct',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\test\\Testpys.sct',
   'DATA'),
  ('win32com\\test\\__init__.py',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\test\\__init__.py',
   'DATA'),
  ('win32com\\test\\daodump.py',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\test\\daodump.py',
   'DATA'),
  ('win32com\\test\\errorSemantics.py',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\test\\errorSemantics.py',
   'DATA'),
  ('win32com\\test\\pippo.idl',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\test\\pippo.idl',
   'DATA'),
  ('win32com\\test\\pippo_server.py',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\test\\pippo_server.py',
   'DATA'),
  ('win32com\\test\\policySemantics.py',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\test\\policySemantics.py',
   'DATA'),
  ('win32com\\test\\readme.txt',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\test\\readme.txt',
   'DATA'),
  ('win32com\\test\\testADOEvents.py',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\test\\testADOEvents.py',
   'DATA'),
  ('win32com\\test\\testAXScript.py',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\test\\testAXScript.py',
   'DATA'),
  ('win32com\\test\\testAccess.py',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\test\\testAccess.py',
   'DATA'),
  ('win32com\\test\\testArrays.py',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\test\\testArrays.py',
   'DATA'),
  ('win32com\\test\\testClipboard.py',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\test\\testClipboard.py',
   'DATA'),
  ('win32com\\test\\testCollections.py',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\test\\testCollections.py',
   'DATA'),
  ('win32com\\test\\testConversionErrors.py',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\test\\testConversionErrors.py',
   'DATA'),
  ('win32com\\test\\testDCOM.py',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\test\\testDCOM.py',
   'DATA'),
  ('win32com\\test\\testDates.py',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\test\\testDates.py',
   'DATA'),
  ('win32com\\test\\testDictionary.py',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\test\\testDictionary.py',
   'DATA'),
  ('win32com\\test\\testDictionary.vbs',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\test\\testDictionary.vbs',
   'DATA'),
  ('win32com\\test\\testDynamic.py',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\test\\testDynamic.py',
   'DATA'),
  ('win32com\\test\\testExchange.py',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\test\\testExchange.py',
   'DATA'),
  ('win32com\\test\\testExplorer.py',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\test\\testExplorer.py',
   'DATA'),
  ('win32com\\test\\testGIT.py',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\test\\testGIT.py',
   'DATA'),
  ('win32com\\test\\testGatewayAddresses.py',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\test\\testGatewayAddresses.py',
   'DATA'),
  ('win32com\\test\\testInterp.vbs',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\test\\testInterp.vbs',
   'DATA'),
  ('win32com\\test\\testIterators.py',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\test\\testIterators.py',
   'DATA'),
  ('win32com\\test\\testMSOffice.py',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\test\\testMSOffice.py',
   'DATA'),
  ('win32com\\test\\testMSOfficeEvents.py',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\test\\testMSOfficeEvents.py',
   'DATA'),
  ('win32com\\test\\testMarshal.py',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\test\\testMarshal.py',
   'DATA'),
  ('win32com\\test\\testNetscape.py',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\test\\testNetscape.py',
   'DATA'),
  ('win32com\\test\\testPersist.py',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\test\\testPersist.py',
   'DATA'),
  ('win32com\\test\\testPippo.py',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\test\\testPippo.py',
   'DATA'),
  ('win32com\\test\\testPyComTest.py',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\test\\testPyComTest.py',
   'DATA'),
  ('win32com\\test\\testPyScriptlet.js',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\test\\testPyScriptlet.js',
   'DATA'),
  ('win32com\\test\\testROT.py',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\test\\testROT.py',
   'DATA'),
  ('win32com\\test\\testServers.py',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\test\\testServers.py',
   'DATA'),
  ('win32com\\test\\testShell.py',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\test\\testShell.py',
   'DATA'),
  ('win32com\\test\\testStorage.py',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\test\\testStorage.py',
   'DATA'),
  ('win32com\\test\\testStreams.py',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\test\\testStreams.py',
   'DATA'),
  ('win32com\\test\\testWMI.py',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\test\\testWMI.py',
   'DATA'),
  ('win32com\\test\\testall.py',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\test\\testall.py',
   'DATA'),
  ('win32com\\test\\testmakepy.py',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\test\\testmakepy.py',
   'DATA'),
  ('win32com\\test\\testvb.py',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\test\\testvb.py',
   'DATA'),
  ('win32com\\test\\testvbscript_regexp.py',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\test\\testvbscript_regexp.py',
   'DATA'),
  ('win32com\\test\\testxslt.js',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\test\\testxslt.js',
   'DATA'),
  ('win32com\\test\\testxslt.py',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\test\\testxslt.py',
   'DATA'),
  ('win32com\\test\\testxslt.xsl',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\test\\testxslt.xsl',
   'DATA'),
  ('win32com\\test\\util.py',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\test\\util.py',
   'DATA'),
  ('win32com\\universal.py',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\universal.py',
   'DATA'),
  ('win32com\\util.py',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\win32com\\util.py',
   'DATA'),
  ('importlib_metadata-7.1.0.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\importlib_metadata-7.1.0.dist-info\\WHEEL',
   'DATA'),
  ('importlib_metadata-7.1.0.dist-info\\LICENSE',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\importlib_metadata-7.1.0.dist-info\\LICENSE',
   'DATA'),
  ('importlib_metadata-7.1.0.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\importlib_metadata-7.1.0.dist-info\\top_level.txt',
   'DATA'),
  ('importlib_metadata-7.1.0.dist-info\\RECORD',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\importlib_metadata-7.1.0.dist-info\\RECORD',
   'DATA'),
  ('importlib_metadata-7.1.0.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\importlib_metadata-7.1.0.dist-info\\INSTALLER',
   'DATA'),
  ('importlib_metadata-7.1.0.dist-info\\METADATA',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\importlib_metadata-7.1.0.dist-info\\METADATA',
   'DATA'),
  ('importlib_metadata-7.1.0.dist-info\\REQUESTED',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\importlib_metadata-7.1.0.dist-info\\REQUESTED',
   'DATA'),
  ('wheel-0.43.0.dist-info\\RECORD',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\wheel-0.43.0.dist-info\\RECORD',
   'DATA'),
  ('wheel-0.43.0.dist-info\\entry_points.txt',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\wheel-0.43.0.dist-info\\entry_points.txt',
   'DATA'),
  ('wheel-0.43.0.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\wheel-0.43.0.dist-info\\INSTALLER',
   'DATA'),
  ('wheel-0.43.0.dist-info\\LICENSE.txt',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\wheel-0.43.0.dist-info\\LICENSE.txt',
   'DATA'),
  ('wheel-0.43.0.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\wheel-0.43.0.dist-info\\WHEEL',
   'DATA'),
  ('wheel-0.43.0.dist-info\\REQUESTED',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\wheel-0.43.0.dist-info\\REQUESTED',
   'DATA'),
  ('wheel-0.43.0.dist-info\\METADATA',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\wheel-0.43.0.dist-info\\METADATA',
   'DATA'),
  ('base_library.zip',
   'C:\\my\\umlagerung\\build\\sap_orchestrator\\base_library.zip',
   'DATA')],
 [],
 False,
 False,
 **********,
 [('run.exe',
   'C:\\Users\\<USER>\\OneDrive - '
   'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\Lib\\site-packages\\PyInstaller\\bootloader\\Windows-64bit-intel\\run.exe',
   'EXECUTABLE')],
 'C:\\Users\\<USER>\\OneDrive - '
 'Lapp\\Desktop\\Dev\\WPy64-31241\\python-3.12.4.amd64\\python312.dll')
