@echo off
setlocal enableextensions
cd /d "%~dp0"

echo ========================================
echo    SAP Orchestrator - KOMMANDOZEILE
echo ========================================
echo.
echo Verwendung:
echo   sap_orchestrator.exe --charge CHARGENNUMMER
echo.
echo Beispiel:
echo   sap_orchestrator.exe --charge 1234567890
echo.
echo Weitere Optionen:
echo   --dry-run          Testlauf ohne SAP
echo   --start-step N     Starte bei Schritt N
echo   --resume           Fortsetzen vom Checkpoint
echo   --keep-sap-open    SAP offen lassen
echo.
cmd /k
