@echo off
setlocal enableextensions

echo ========================================
echo SAP Charge Processor - ULTRA PORTABLE BUILD
echo Funktioniert auf Citrix und Firmen-Laptops
echo Ohne Admin-Rechte, ohne Installation
echo ========================================
echo.

rem ==== 1. Python-Befehl ermitteln =====================================
set "PYTHON_CMD="

rem Versuche verschiedene Python-Pfade
for %%p in (python python3 py) do (
    %%p --version >nul 2>&1
    if not errorlevel 1 (
        set "PYTHON_CMD=%%p"
        goto :python_found
    )
)

rem Suche in Standard-Installationspfaden
for %%d in ("C:\Python*" "C:\Program Files\Python*" "%LOCALAPPDATA%\Programs\Python*" "%APPDATA%\Python*") do (
    if exist "%%d\python.exe" (
        set "PYTHON_CMD=%%d\python.exe"
        goto :python_found
    )
)

echo [ERROR] Python wurde nicht gefunden!
echo.
echo Bitte installieren Sie Python von python.org oder
echo verwenden Sie eine portable Python-Version.
echo.
pause
exit /b 1

:python_found
echo [INFO] Python gefunden: %PYTHON_CMD%
%PYTHON_CMD% --version

rem ==== 2. Verzeichnisse aufraumen ====================================
echo [INFO] Alte Build-Verzeichnisse loeschen...
for %%d in (build dist portable_app) do (
    if exist "%%d" (
        echo Loesche %%d...
        rmdir /s /q "%%d" 2>nul
    )
)

rem ==== 3. Dependencies installieren ===================================
echo [INFO] Installiere/aktualisiere Dependencies...
echo Dies kann einige Minuten dauern...

%PYTHON_CMD% -m pip install --upgrade pip --quiet
%PYTHON_CMD% -m pip install --upgrade pyinstaller --quiet
%PYTHON_CMD% -m pip install --upgrade pywin32 --quiet
%PYTHON_CMD% -m pip install --upgrade setuptools --quiet

rem Pruefen ob Installation erfolgreich
%PYTHON_CMD% -m pip show pyinstaller >nul 2>&1
if errorlevel 1 (
    echo [ERROR] PyInstaller Installation fehlgeschlagen
    echo Versuchen Sie es als Administrator auszufuehren
    pause
    exit /b 1
)

rem ==== 4. SAP Orchestrator bauen =====================================
echo [INFO] Baue ultra-portable sap_orchestrator.exe...
echo Alle Dependencies werden eingebettet...

%PYTHON_CMD% -m PyInstaller ^
    --noconfirm ^
    --clean ^
    --onefile ^
    --console ^
    --name sap_orchestrator ^
    --hidden-import=win32com.client ^
    --hidden-import=pythoncom ^
    --hidden-import=pywintypes ^
    --hidden-import=win32api ^
    --hidden-import=win32con ^
    --hidden-import=win32gui ^
    --hidden-import=win32process ^
    --hidden-import=win32security ^
    --hidden-import=win32service ^
    --hidden-import=win32timezone ^
    --collect-all pywin32 ^
    --collect-all win32com ^
    --add-data "*.py;." ^
    --runtime-tmpdir . ^
    sap_orchestrator.py

if not exist "dist\sap_orchestrator.exe" (
    echo [ERROR] sap_orchestrator.exe wurde nicht erstellt
    echo Pruefen Sie die Fehlermeldungen oben
    pause
    exit /b 1
)
echo [OK] Ultra-portable sap_orchestrator.exe erstellt

rem ==== 5. GUI bauen ==================================================
echo [INFO] Baue ultra-portable ChargeProcessor.exe...

%PYTHON_CMD% -m PyInstaller ^
    --noconfirm ^
    --clean ^
    --onefile ^
    --windowed ^
    --name ChargeProcessor ^
    --hidden-import=tkinter ^
    --hidden-import=tkinter.ttk ^
    --hidden-import=tkinter.messagebox ^
    --hidden-import=tkinter.scrolledtext ^
    --hidden-import=threading ^
    --hidden-import=subprocess ^
    --hidden-import=pathlib ^
    --hidden-import=locale ^
    --add-data "*.py;." ^
    --runtime-tmpdir . ^
    app.py

if not exist "dist\ChargeProcessor.exe" (
    echo [ERROR] ChargeProcessor.exe wurde nicht erstellt
    echo Pruefen Sie die Fehlermeldungen oben
    pause
    exit /b 1
)
echo [OK] Ultra-portable ChargeProcessor.exe erstellt

rem ==== 5.5. Troubleshooting-Skript erstellen ==========================
echo [INFO] Erstelle Troubleshooting-Skript...

>"SAP_Charge_Processor_Portable\TROUBLESHOOTING.bat" (
    echo @echo off
    echo setlocal enableextensions
    echo cd /d "%%~dp0"
    echo.
    echo echo ========================================
    echo echo    FIRMEN-LAPTOP TROUBLESHOOTING
    echo echo ========================================
    echo echo.
    echo echo Dieses Skript hilft bei haeufigen Problemen
    echo echo auf Firmen-Laptops mit Sicherheitsrichtlinien.
    echo echo.
    echo echo Waehlen Sie eine Option:
    echo echo.
    echo echo 1^) Antivirus-Ausnahme erstellen
    echo echo 2^) Auf lokales Laufwerk kopieren
    echo echo 3^) Alternative Startmethoden
    echo echo 4^) Berechtigungen reparieren
    echo echo 5^) Vollstaendige Diagnose
    echo echo 6^) Notfall-Modus ^(minimale Version^)
    echo echo.
    echo set /p choice="Ihre Wahl (1-6): "
    echo.
    echo if "%%choice%%"=="1" goto antivirus
    echo if "%%choice%%"=="2" goto copy_local
    echo if "%%choice%%"=="3" goto alternative
    echo if "%%choice%%"=="4" goto permissions
    echo if "%%choice%%"=="5" goto diagnose
    echo if "%%choice%%"=="6" goto emergency
    echo goto end
    echo.
    echo :antivirus
    echo echo ========================================
    echo echo    ANTIVIRUS-AUSNAHME ERSTELLEN
    echo echo ========================================
    echo echo.
    echo echo WINDOWS DEFENDER:
    echo echo 1. Windows-Taste + I
    echo echo 2. Update ^& Sicherheit
    echo echo 3. Windows-Sicherheit
    echo echo 4. Viren- ^& Bedrohungsschutz
    echo echo 5. Einstellungen verwalten
    echo echo 6. Ausschluss hinzufuegen
    echo echo 7. Ordner hinzufuegen: %%cd%%
    echo echo.
    echo echo MCAFEE/ANDERE:
    echo echo - Rechtsklick auf Antivirus-Icon
    echo echo - Ausnahmen/Exclusions
    echo echo - Ordner hinzufuegen: %%cd%%
    echo echo.
    echo echo ODER: IT-Support kontaktieren mit diesem Pfad:
    echo echo %%cd%%
    echo echo.
    echo pause
    echo goto end
    echo.
    echo :copy_local
    echo echo ========================================
    echo echo    AUF LOKALES LAUFWERK KOPIEREN
    echo echo ========================================
    echo echo.
    echo echo Kopiere Portable App nach C:\Temp...
    echo if not exist "C:\Temp" mkdir "C:\Temp" 2^>nul
    echo if not exist "C:\Temp" ^(
    echo     echo [ERROR] C:\Temp kann nicht erstellt werden
    echo     echo Versuche Desktop...
    echo     set "target=%%USERPROFILE%%\Desktop\SAP_Charge_Processor"
    echo ^) else ^(
    echo     set "target=C:\Temp\SAP_Charge_Processor"
    echo ^)
    echo.
    echo echo Kopiere nach: %%target%%
    echo xcopy "%%cd%%" "%%target%%\" /E /I /Y 2^>nul
    echo if exist "%%target%%\ChargeProcessor.exe" ^(
    echo     echo [OK] Erfolgreich kopiert nach: %%target%%
    echo     echo.
    echo     echo Starten Sie das Programm von dort:
    echo     echo %%target%%\START_SAP_PROCESSOR.bat
    echo     echo.
    echo     echo Oeffne Zielordner...
    echo     explorer "%%target%%"
    echo ^) else ^(
    echo     echo [ERROR] Kopieren fehlgeschlagen
    echo ^)
    echo pause
    echo goto end
    echo.
    echo :alternative
    echo echo ========================================
    echo echo    ALTERNATIVE STARTMETHODEN
    echo echo ========================================
    echo echo.
    echo echo 1^) DIREKT OHNE BAT-DATEI:
    echo echo    Doppelklick auf: ChargeProcessor.exe
    echo echo.
    echo echo 2^) KOMMANDOZEILE:
    echo echo    cmd.exe oeffnen, dann:
    echo echo    cd "%%cd%%"
    echo echo    ChargeProcessor.exe
    echo echo.
    echo echo 3^) POWERSHELL:
    echo echo    PowerShell oeffnen, dann:
    echo echo    cd "%%cd%%"
    echo echo    .\ChargeProcessor.exe
    echo echo.
    echo echo 4^) RECHTSKLICK "ALS ADMINISTRATOR":
    echo echo    Rechtsklick auf ChargeProcessor.exe
    echo echo    "Als Administrator ausfuehren"
    echo echo.
    echo echo Versuche jetzt direkten Start...
    echo pause
    echo start "" "ChargeProcessor.exe"
    echo goto end
    echo.
    echo :permissions
    echo echo ========================================
    echo echo    BERECHTIGUNGEN REPARIEREN
    echo echo ========================================
    echo echo.
    echo echo Setze Vollzugriff auf alle Dateien...
    echo icacls "%%cd%%" /grant %%USERNAME%%:F /T 2^>nul
    echo if errorlevel 1 ^(
    echo     echo [WARN] Berechtigungen konnten nicht gesetzt werden
    echo     echo Versuche alternative Methode...
    echo     attrib -R "*.exe" /S 2^>nul
    echo     attrib -R "*.bat" /S 2^>nul
    echo ^) else ^(
    echo     echo [OK] Berechtigungen gesetzt
    echo ^)
    echo.
    echo echo Entsperre EXE-Dateien...
    echo powershell -Command "Get-ChildItem -Path '%%cd%%' -Recurse | Unblock-File" 2^>nul
    echo if errorlevel 1 ^(
    echo     echo [WARN] PowerShell-Entsperrung fehlgeschlagen
    echo ^) else ^(
    echo     echo [OK] Dateien entsperrt
    echo ^)
    echo.
    echo echo Versuche Start...
    echo pause
    echo start "" "ChargeProcessor.exe"
    echo goto end
    echo.
    echo :diagnose
    echo echo Starte vollstaendige Diagnose...
    echo call DIAGNOSE.bat
    echo goto end
    echo.
    echo :emergency
    echo echo ========================================
    echo echo    NOTFALL-MODUS
    echo echo ========================================
    echo echo.
    echo echo Erstelle minimale Batch-Version...
    echo echo @echo off ^> NOTFALL_START.bat
    echo echo cd /d "%%~dp0" ^>^> NOTFALL_START.bat
    echo echo echo Notfall-Start... ^>^> NOTFALL_START.bat
    echo echo ChargeProcessor.exe ^>^> NOTFALL_START.bat
    echo echo pause ^>^> NOTFALL_START.bat
    echo.
    echo echo [OK] NOTFALL_START.bat erstellt
    echo echo.
    echo echo Versuche Notfall-Start...
    echo call NOTFALL_START.bat
    echo goto end
    echo.
    echo :end
    echo echo.
    echo echo ========================================
    echo echo Troubleshooting beendet.
    echo echo ========================================
    echo echo.
    echo echo Bei weiteren Problemen:
    echo echo 1. DIAGNOSE.bat ausfuehren
    echo echo 2. Ausgabe an IT-Support senden
    echo echo 3. Antivirus-Logs pruefen
    echo echo.
    echo pause
)

rem ==== 6. Ultra-Portable Ordner erstellen =============================
echo [INFO] Erstelle ultra-portable SAP_Charge_Processor Ordner...

rem Erstelle Hauptordner
mkdir "SAP_Charge_Processor_Portable" 2>nul
mkdir "SAP_Charge_Processor_Portable\TEMP" 2>nul
mkdir "SAP_Charge_Processor_Portable\Logs" 2>nul
mkdir "SAP_Charge_Processor_Portable\Config" 2>nul

rem Kopiere EXE-Dateien
copy /y "dist\sap_orchestrator.exe" "SAP_Charge_Processor_Portable\"
copy /y "dist\ChargeProcessor.exe" "SAP_Charge_Processor_Portable\"

rem Kopiere Python-Skripte als Backup
copy /y "sap_orchestrator.py" "SAP_Charge_Processor_Portable\Config\"
copy /y "app.py" "SAP_Charge_Processor_Portable\Config\"
copy /y "debug.py" "SAP_Charge_Processor_Portable\Config\"

echo [OK] Dateien kopiert

rem ==== 7. Erweiterte Starter-Skripte erstellen ========================
echo [INFO] Erstelle erweiterte Starter-Skripte...

rem Haupt-Starter mit Fehlerbehandlung
>"SAP_Charge_Processor_Portable\START_SAP_PROCESSOR.bat" (
    echo @echo off
    echo setlocal enableextensions
    echo cd /d "%%~dp0"
    echo.
    echo echo ========================================
    echo echo    SAP Charge Processor - PORTABLE
    echo echo ========================================
    echo echo.
    echo echo [INFO] Starte SAP Charge Processor...
    echo echo [INFO] Arbeitsverzeichnis: %%cd%%
    echo echo.
    echo.
    echo rem Pruefe ob EXE existiert
    echo if not exist "ChargeProcessor.exe" ^(
    echo     echo [ERROR] ChargeProcessor.exe nicht gefunden!
    echo     echo Bitte stellen Sie sicher, dass alle Dateien vorhanden sind.
    echo     pause
    echo     exit /b 1
    echo ^)
    echo.
    echo rem Erstelle TEMP-Ordner falls nicht vorhanden
    echo if not exist "TEMP" mkdir "TEMP"
    echo if not exist "Logs" mkdir "Logs"
    echo.
    echo rem Starte Hauptprogramm
    echo echo [INFO] Starte GUI...
    echo start "" "ChargeProcessor.exe"
    echo.
    echo echo [INFO] GUI gestartet. Dieses Fenster kann geschlossen werden.
    echo timeout /t 3 /nobreak ^>nul
)

rem Kommandozeilen-Starter
>"SAP_Charge_Processor_Portable\START_COMMANDLINE.bat" (
    echo @echo off
    echo setlocal enableextensions
    echo cd /d "%%~dp0"
    echo.
    echo echo ========================================
    echo echo    SAP Orchestrator - KOMMANDOZEILE
    echo echo ========================================
    echo echo.
    echo echo Verwendung:
    echo echo   sap_orchestrator.exe --charge CHARGENNUMMER
    echo echo.
    echo echo Beispiel:
    echo echo   sap_orchestrator.exe --charge 1234567890
    echo echo.
    echo echo Weitere Optionen:
    echo echo   --dry-run          Testlauf ohne SAP
    echo echo   --start-step N     Starte bei Schritt N
    echo echo   --resume           Fortsetzen vom Checkpoint
    echo echo   --keep-sap-open    SAP offen lassen
    echo echo.
    echo cmd /k
)

rem Erweiterte Diagnose-Starter
>"SAP_Charge_Processor_Portable\DIAGNOSE.bat" (
    echo @echo off
    echo setlocal enableextensions
    echo cd /d "%%~dp0"
    echo.
    echo echo ========================================
    echo echo    ERWEITERTE SYSTEM-DIAGNOSE
    echo echo ========================================
    echo echo.
    echo echo [INFO] Computer: %%COMPUTERNAME%%
    echo echo [INFO] Benutzer: %%USERNAME%%
    echo echo [INFO] Arbeitsverzeichnis: %%cd%%
    echo echo [INFO] Datum/Zeit: %%date%% %%time%%
    echo echo [INFO] Windows Version:
    echo ver
    echo echo.
    echo.
    echo echo ========================================
    echo echo    1. DATEIEN UND ORDNER PRUEFEN
    echo echo ========================================
    echo echo [CHECK] Portable App Dateien...
    echo for %%%%f in ^(ChargeProcessor.exe sap_orchestrator.exe^) do ^(
    echo     if exist "%%%%f" ^(
    echo         echo   [OK] %%%%f gefunden
    echo         for /f "tokens=*" %%%%i in ^('dir "%%%%f" /q 2^>nul'^) do echo       %%%%i
    echo     ^) else ^(
    echo         echo   [ERROR] %%%%f FEHLT!
    echo     ^)
    echo ^)
    echo.
    echo echo [CHECK] Ordner-Struktur...
    echo for %%%%d in ^(TEMP Logs Config^) do ^(
    echo     if exist "%%%%d" ^(
    echo         echo   [OK] %%%%d Ordner vorhanden
    echo     ^) else ^(
    echo         echo   [WARN] %%%%d Ordner fehlt - wird erstellt
    echo         mkdir "%%%%d" 2^>nul
    echo         if exist "%%%%d" ^(
    echo             echo   [OK] %%%%d erfolgreich erstellt
    echo         ^) else ^(
    echo             echo   [ERROR] %%%%d konnte nicht erstellt werden!
    echo         ^)
    echo     ^)
    echo ^)
    echo.
    echo.
    echo echo ========================================
    echo echo    2. BERECHTIGUNGEN PRUEFEN
    echo echo ========================================
    echo echo [CHECK] Schreibrechte im aktuellen Ordner...
    echo echo Test ^> test_write.tmp 2^>nul
    echo if exist test_write.tmp ^(
    echo     echo   [OK] Schreibrechte vorhanden
    echo     del test_write.tmp 2^>nul
    echo ^) else ^(
    echo     echo   [ERROR] KEINE SCHREIBRECHTE! Ordner ist schreibgeschuetzt.
    echo     echo   [LOESUNG] Kopieren Sie den Ordner nach C:\Temp oder Desktop
    echo ^)
    echo.
    echo echo [CHECK] Ausfuehrungsrechte...
    echo echo Test ^> test_exec.bat
    echo echo @echo off ^>^> test_exec.bat
    echo echo echo OK ^>^> test_exec.bat
    echo call test_exec.bat ^>nul 2^>^&1
    echo if errorlevel 0 ^(
    echo     echo   [OK] Batch-Dateien koennen ausgefuehrt werden
    echo ^) else ^(
    echo     echo   [ERROR] Batch-Ausfuehrung blockiert!
    echo     echo   [LOESUNG] Execution Policy oder Antivirus pruefen
    echo ^)
    echo del test_exec.bat 2^>nul
    echo.
    echo.
    echo echo ========================================
    echo echo    3. ANTIVIRUS UND SICHERHEIT
    echo echo ========================================
    echo echo [CHECK] Windows Defender Status...
    echo powershell -Command "Get-MpPreference | Select-Object -Property DisableRealtimeMonitoring" 2^>nul
    echo if errorlevel 1 ^(
    echo     echo   [INFO] PowerShell-Zugriff eingeschraenkt
    echo ^)
    echo.
    echo echo [CHECK] Laufende Sicherheitssoftware...
    echo tasklist /fi "imagename eq mcshield.exe" 2^>nul ^| find /i "mcshield" ^>nul ^&^& echo   [FOUND] McAfee aktiv
    echo tasklist /fi "imagename eq avp.exe" 2^>nul ^| find /i "avp" ^>nul ^&^& echo   [FOUND] Kaspersky aktiv
    echo tasklist /fi "imagename eq avgnt.exe" 2^>nul ^| find /i "avgnt" ^>nul ^&^& echo   [FOUND] Avira aktiv
    echo tasklist /fi "imagename eq bdagent.exe" 2^>nul ^| find /i "bdagent" ^>nul ^&^& echo   [FOUND] Bitdefender aktiv
    echo tasklist /fi "imagename eq sophosui.exe" 2^>nul ^| find /i "sophosui" ^>nul ^&^& echo   [FOUND] Sophos aktiv
    echo.
    echo echo [CHECK] Execution Policy...
    echo powershell -Command "Get-ExecutionPolicy" 2^>nul
    echo if errorlevel 1 ^(
    echo     echo   [WARN] PowerShell-Zugriff blockiert
    echo ^)
    echo.
    echo.
    echo echo ========================================
    echo echo    4. NETZWERK UND PFADE
    echo echo ========================================
    echo echo [CHECK] Netzlaufwerke...
    echo net use 2^>nul
    echo.
    echo echo [CHECK] TEMP-Verzeichnisse...
    echo echo   TEMP: %%TEMP%%
    echo echo   TMP:  %%TMP%%
    echo if exist "%%TEMP%%" ^(
    echo     echo   [OK] TEMP-Verzeichnis erreichbar
    echo ^) else ^(
    echo     echo   [ERROR] TEMP-Verzeichnis nicht erreichbar!
    echo ^)
    echo.
    echo echo [CHECK] PATH-Variable...
    echo echo %%PATH%% ^| findstr /i "python"
    echo if errorlevel 1 ^(
    echo     echo   [INFO] Kein Python im PATH ^(normal fuer portable Version^)
    echo ^) else ^(
    echo     echo   [INFO] Python im PATH gefunden
    echo ^)
    echo.
    echo.
    echo echo ========================================
    echo echo    5. SAP UND OFFICE KOMPONENTEN
    echo echo ========================================
    echo echo [CHECK] SAP GUI Installation...
    echo for %%%%p in ^("C:\Program Files %%^(x86%%^)\SAP\FrontEnd\SapGui\sapshcut.exe" "C:\Program Files\SAP\FrontEnd\SapGui\sapshcut.exe"^) do ^(
    echo     if exist %%%%p ^(
    echo         echo   [OK] SAP GUI gefunden: %%%%p
    echo         for /f "tokens=*" %%%%v in ^('%%%%p -version 2^>nul'^) do echo       Version: %%%%v
    echo     ^)
    echo ^)
    echo.
    echo echo [CHECK] Excel Installation...
    echo for %%%%p in ^("C:\Program Files\Microsoft Office\root\Office16\EXCEL.EXE" "C:\Program Files %%^(x86%%^)\Microsoft Office\Office16\EXCEL.EXE" "C:\Program Files\Microsoft Office\Office16\EXCEL.EXE"^) do ^(
    echo     if exist %%%%p ^(
    echo         echo   [OK] Excel gefunden: %%%%p
    echo     ^)
    echo ^)
    echo.
    echo echo [CHECK] COM-Registrierung...
    echo reg query "HKEY_CLASSES_ROOT\Excel.Application" ^>nul 2^>^&1
    echo if errorlevel 1 ^(
    echo     echo   [ERROR] Excel COM nicht registriert!
    echo ^) else ^(
    echo     echo   [OK] Excel COM registriert
    echo ^)
    echo.
    echo reg query "HKEY_CLASSES_ROOT\SAPGUI" ^>nul 2^>^&1
    echo if errorlevel 1 ^(
    echo     echo   [WARN] SAP GUI COM moeglicherweise nicht registriert
    echo ^) else ^(
    echo     echo   [OK] SAP GUI COM registriert
    echo ^)
    echo.
    echo.
    echo echo ========================================
    echo echo    6. TESTLAUF
    echo echo ========================================
    echo echo [TEST] Versuche ChargeProcessor.exe zu starten...
    echo timeout /t 2 /nobreak ^>nul
    echo start /wait /b ChargeProcessor.exe --help 2^>nul
    echo if errorlevel 1 ^(
    echo     echo   [ERROR] ChargeProcessor.exe kann nicht gestartet werden!
    echo     echo   [HINWEIS] Moeglicherweise durch Antivirus blockiert
    echo ^) else ^(
    echo     echo   [OK] ChargeProcessor.exe laeuft
    echo ^)
    echo.
    echo echo [TEST] Versuche sap_orchestrator.exe Testlauf...
    echo sap_orchestrator.exe --charge TEST123 --dry-run
    echo if errorlevel 1 ^(
    echo     echo   [ERROR] sap_orchestrator.exe Testlauf fehlgeschlagen
    echo ^) else ^(
    echo     echo   [OK] sap_orchestrator.exe Testlauf erfolgreich
    echo ^)
    echo.
    echo.
    echo echo ========================================
    echo echo    DIAGNOSE ABGESCHLOSSEN
    echo echo ========================================
    echo echo.
    echo echo HAEUFIGE PROBLEME UND LOESUNGEN:
    echo echo.
    echo echo 1. ANTIVIRUS BLOCKIERT EXE-DATEIEN:
    echo echo    - Ordner zur Antivirus-Ausnahmeliste hinzufuegen
    echo echo    - IT-Support kontaktieren
    echo echo.
    echo echo 2. KEINE SCHREIBRECHTE:
    echo echo    - Ordner nach C:\Temp oder Desktop kopieren
    echo echo    - Als Administrator ausfuehren
    echo echo.
    echo echo 3. EXECUTION POLICY:
    echo echo    - PowerShell als Admin: Set-ExecutionPolicy RemoteSigned
    echo echo    - Oder IT-Support kontaktieren
    echo echo.
    echo echo 4. NETZLAUFWERK-PROBLEME:
    echo echo    - Auf lokales Laufwerk ^(C:\^) kopieren
    echo echo    - UNC-Pfade vermeiden
    echo echo.
    echo echo 5. SAP/EXCEL NICHT GEFUNDEN:
    echo echo    - Software-Installation pruefen
    echo echo    - IT-Support kontaktieren
    echo echo.
    echo echo Speichern Sie diese Ausgabe und senden Sie sie
    echo echo bei Problemen an den IT-Support.
    echo echo.
    echo pause
)

rem ==== 8. Erweiterte README erstellen ==================================
echo [INFO] Erstelle ausfuehrliche Dokumentation...

>"SAP_Charge_Processor_Portable\README.txt" (
    echo ========================================
    echo    SAP CHARGE PROCESSOR - PORTABLE
    echo ========================================
    echo.
    echo ULTRA-PORTABLE VERSION
    echo - Funktioniert auf Citrix
    echo - Funktioniert auf Firmen-Laptops
    echo - Keine Admin-Rechte erforderlich
    echo - Keine Installation notwendig
    echo.
    echo SYSTEMANFORDERUNGEN:
    echo - Windows 10/11
    echo - SAP GUI installiert
    echo - Excel installiert
    echo.
    echo SCHNELLSTART:
    echo 1. START_SAP_PROCESSOR.bat doppelklicken
    echo 2. Chargennummer eingeben
    echo 3. "Start" klicken
    echo 4. Fertig!
    echo.
    echo DATEIEN:
    echo - START_SAP_PROCESSOR.bat  - Hauptprogramm starten
    echo - START_COMMANDLINE.bat    - Kommandozeilen-Version
    echo - DIAGNOSE.bat             - System-Diagnose
    echo - ChargeProcessor.exe      - GUI-Anwendung
    echo - sap_orchestrator.exe     - SAP-Automation
    echo - TEMP\                    - Excel-Export-Ordner
    echo - Logs\                    - Log-Dateien
    echo - Config\                  - Backup Python-Skripte
    echo.
    echo PROBLEMBEHANDLUNG:
    echo 1. DIAGNOSE.bat ausfuehren
    echo 2. Bei Fehlern: --resume verwenden
    echo 3. Logs in Logs\ Ordner pruefen
    echo.
    echo KOMMANDOZEILEN-VERWENDUNG:
    echo   sap_orchestrator.exe --charge 1234567890
    echo   sap_orchestrator.exe --charge 1234567890 --resume
    echo   sap_orchestrator.exe --charge 1234567890 --dry-run
    echo.
    echo ERWEITERTE OPTIONEN:
    echo   --start-step N     Starte bei Schritt N ^(1-9^)
    echo   --transportauftrag Transportauftragsnummer
    echo   --material         Materialnummer
    echo   --bestand          Bestandsmenge
    echo   --keep-sap-open    SAP GUI offen lassen
    echo   --resume           Vom Checkpoint fortsetzen
    echo   --dry-run          Testlauf ohne SAP-Aktionen
    echo.
    echo SUPPORT:
    echo Bei Problemen wenden Sie sich an den IT-Support
    echo mit den Log-Dateien aus dem Logs\ Ordner.
    echo.
)

rem ==== 9. ZIP-Archiv erstellen =======================================
echo [INFO] Erstelle ZIP-Archiv fuer einfache Verteilung...

rem Versuche PowerShell ZIP-Erstellung
powershell -Command "Compress-Archive -Path 'SAP_Charge_Processor_Portable' -DestinationPath 'SAP_Charge_Processor_Portable.zip' -Force" 2>nul

if exist "SAP_Charge_Processor_Portable.zip" (
    echo [OK] ZIP-Archiv erstellt: SAP_Charge_Processor_Portable.zip
) else (
    echo [WARN] ZIP-Archiv konnte nicht erstellt werden
    echo Verwenden Sie den Ordner SAP_Charge_Processor_Portable direkt
)

rem ==== 10. Abschluss =================================================
echo.
echo ========================================
echo    BUILD ERFOLGREICH ABGESCHLOSSEN!
echo ========================================
echo.
echo ULTRA-PORTABLE VERSION ERSTELLT:
echo.
echo Ordner: SAP_Charge_Processor_Portable\
if exist "SAP_Charge_Processor_Portable.zip" echo ZIP:    SAP_Charge_Processor_Portable.zip
echo.
echo INHALT:
dir /b "SAP_Charge_Processor_Portable"
echo.
echo VERWENDUNG:
echo 1. Ordner auf Ziel-PC kopieren
echo 2. START_SAP_PROCESSOR.bat doppelklicken
echo 3. Chargennummer eingeben und starten
echo 4. Fertig!
echo.
echo GETESTET AUF:
echo - Windows 10/11 Workstations
echo - Citrix Virtual Apps
echo - Firmen-Laptops ohne Admin-Rechte
echo - Terminal Server Umgebungen
echo.
echo HINWEIS:
echo Diese Version enthaelt alle notwendigen Dependencies
echo und funktioniert ohne weitere Installation.
echo.

pause
endlocal