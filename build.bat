@echo off
setlocal enableextensions

echo ========================================
echo SAP Charge Processor - ULTRA PORTABLE BUILD
echo Funktioniert auf Citrix und Firmen-Laptops
echo Ohne Admin-Rechte, ohne Installation
echo ========================================
echo.

rem ==== 1. Python-Befehl ermitteln =====================================
set "PYTHON_CMD="

rem Versuche verschiedene Python-Pfade
for %%p in (python python3 py) do (
    %%p --version >nul 2>&1
    if not errorlevel 1 (
        set "PYTHON_CMD=%%p"
        goto :python_found
    )
)

rem Suche in Standard-Installationspfaden
for %%d in ("C:\Python*" "C:\Program Files\Python*" "%LOCALAPPDATA%\Programs\Python*" "%APPDATA%\Python*") do (
    if exist "%%d\python.exe" (
        set "PYTHON_CMD=%%d\python.exe"
        goto :python_found
    )
)

echo [ERROR] Python wurde nicht gefunden!
echo.
echo Bitte installieren Sie Python von python.org oder
echo verwenden Sie eine portable Python-Version.
echo.
pause
exit /b 1

:python_found
echo [INFO] Python gefunden: %PYTHON_CMD%
%PYTHON_CMD% --version

rem ==== 2. Verzeichnisse aufraumen ====================================
echo [INFO] Alte Build-Verzeichnisse loeschen...
for %%d in (build dist portable_app) do (
    if exist "%%d" (
        echo Loesche %%d...
        rmdir /s /q "%%d" 2>nul
    )
)

rem ==== 3. Dependencies installieren ===================================
echo [INFO] Installiere/aktualisiere Dependencies...
echo Dies kann einige Minuten dauern...

%PYTHON_CMD% -m pip install --upgrade pip --quiet
%PYTHON_CMD% -m pip install --upgrade pyinstaller --quiet
%PYTHON_CMD% -m pip install --upgrade pywin32 --quiet
%PYTHON_CMD% -m pip install --upgrade setuptools --quiet

rem Pruefen ob Installation erfolgreich
%PYTHON_CMD% -m pip show pyinstaller >nul 2>&1
if errorlevel 1 (
    echo [ERROR] PyInstaller Installation fehlgeschlagen
    echo Versuchen Sie es als Administrator auszufuehren
    pause
    exit /b 1
)

rem ==== 4. SAP Orchestrator bauen =====================================
echo [INFO] Baue ultra-portable sap_orchestrator.exe...
echo Alle Dependencies werden eingebettet...

%PYTHON_CMD% -m PyInstaller ^
    --noconfirm ^
    --clean ^
    --onefile ^
    --console ^
    --name sap_orchestrator ^
    --hidden-import=win32com.client ^
    --hidden-import=pythoncom ^
    --hidden-import=pywintypes ^
    --hidden-import=win32api ^
    --hidden-import=win32con ^
    --hidden-import=win32gui ^
    --hidden-import=win32process ^
    --hidden-import=win32security ^
    --hidden-import=win32service ^
    --hidden-import=win32timezone ^
    --collect-all pywin32 ^
    --collect-all win32com ^
    --add-data "*.py;." ^
    --runtime-tmpdir . ^
    sap_orchestrator.py

if not exist "dist\sap_orchestrator.exe" (
    echo [ERROR] sap_orchestrator.exe wurde nicht erstellt
    echo Pruefen Sie die Fehlermeldungen oben
    pause
    exit /b 1
)
echo [OK] Ultra-portable sap_orchestrator.exe erstellt

rem ==== 5. GUI bauen ==================================================
echo [INFO] Baue ultra-portable ChargeProcessor.exe...

%PYTHON_CMD% -m PyInstaller ^
    --noconfirm ^
    --clean ^
    --onefile ^
    --windowed ^
    --name ChargeProcessor ^
    --hidden-import=tkinter ^
    --hidden-import=tkinter.ttk ^
    --hidden-import=tkinter.messagebox ^
    --hidden-import=tkinter.scrolledtext ^
    --hidden-import=threading ^
    --hidden-import=subprocess ^
    --hidden-import=pathlib ^
    --hidden-import=locale ^
    --add-data "*.py;." ^
    --runtime-tmpdir . ^
    app.py

if not exist "dist\ChargeProcessor.exe" (
    echo [ERROR] ChargeProcessor.exe wurde nicht erstellt
    echo Pruefen Sie die Fehlermeldungen oben
    pause
    exit /b 1
)
echo [OK] Ultra-portable ChargeProcessor.exe erstellt

rem ==== 6. Ultra-Portable Ordner erstellen =============================
echo [INFO] Erstelle ultra-portable SAP_Charge_Processor Ordner...

rem Erstelle Hauptordner
mkdir "SAP_Charge_Processor_Portable" 2>nul
mkdir "SAP_Charge_Processor_Portable\TEMP" 2>nul
mkdir "SAP_Charge_Processor_Portable\Logs" 2>nul
mkdir "SAP_Charge_Processor_Portable\Config" 2>nul

rem Kopiere EXE-Dateien
copy /y "dist\sap_orchestrator.exe" "SAP_Charge_Processor_Portable\"
copy /y "dist\ChargeProcessor.exe" "SAP_Charge_Processor_Portable\"

rem Kopiere Python-Skripte als Backup
copy /y "sap_orchestrator.py" "SAP_Charge_Processor_Portable\Config\"
copy /y "app.py" "SAP_Charge_Processor_Portable\Config\"
copy /y "debug.py" "SAP_Charge_Processor_Portable\Config\"

echo [OK] Dateien kopiert

rem ==== 7. Erweiterte Starter-Skripte erstellen ========================
echo [INFO] Erstelle erweiterte Starter-Skripte...

rem Haupt-Starter mit Fehlerbehandlung
>"SAP_Charge_Processor_Portable\START_SAP_PROCESSOR.bat" (
    echo @echo off
    echo setlocal enableextensions
    echo cd /d "%%~dp0"
    echo.
    echo echo ========================================
    echo echo    SAP Charge Processor - PORTABLE
    echo echo ========================================
    echo echo.
    echo echo [INFO] Starte SAP Charge Processor...
    echo echo [INFO] Arbeitsverzeichnis: %%cd%%
    echo echo.
    echo.
    echo rem Pruefe ob EXE existiert
    echo if not exist "ChargeProcessor.exe" ^(
    echo     echo [ERROR] ChargeProcessor.exe nicht gefunden!
    echo     echo Bitte stellen Sie sicher, dass alle Dateien vorhanden sind.
    echo     pause
    echo     exit /b 1
    echo ^)
    echo.
    echo rem Erstelle TEMP-Ordner falls nicht vorhanden
    echo if not exist "TEMP" mkdir "TEMP"
    echo if not exist "Logs" mkdir "Logs"
    echo.
    echo rem Starte Hauptprogramm
    echo echo [INFO] Starte GUI...
    echo start "" "ChargeProcessor.exe"
    echo.
    echo echo [INFO] GUI gestartet. Dieses Fenster kann geschlossen werden.
    echo timeout /t 3 /nobreak ^>nul
)

rem Kommandozeilen-Starter
>"SAP_Charge_Processor_Portable\START_COMMANDLINE.bat" (
    echo @echo off
    echo setlocal enableextensions
    echo cd /d "%%~dp0"
    echo.
    echo echo ========================================
    echo echo    SAP Orchestrator - KOMMANDOZEILE
    echo echo ========================================
    echo echo.
    echo echo Verwendung:
    echo echo   sap_orchestrator.exe --charge CHARGENNUMMER
    echo echo.
    echo echo Beispiel:
    echo echo   sap_orchestrator.exe --charge 1234567890
    echo echo.
    echo echo Weitere Optionen:
    echo echo   --dry-run          Testlauf ohne SAP
    echo echo   --start-step N     Starte bei Schritt N
    echo echo   --resume           Fortsetzen vom Checkpoint
    echo echo   --keep-sap-open    SAP offen lassen
    echo echo.
    echo cmd /k
)

rem Diagnose-Starter
>"SAP_Charge_Processor_Portable\DIAGNOSE.bat" (
    echo @echo off
    echo setlocal enableextensions
    echo cd /d "%%~dp0"
    echo.
    echo echo ========================================
    echo echo    SYSTEM-DIAGNOSE
    echo echo ========================================
    echo echo.
    echo echo [INFO] Arbeitsverzeichnis: %%cd%%
    echo echo [INFO] Datum/Zeit: %%date%% %%time%%
    echo echo.
    echo echo [CHECK] Dateien pruefen...
    echo for %%%%f in ^(ChargeProcessor.exe sap_orchestrator.exe^) do ^(
    echo     if exist "%%%%f" ^(
    echo         echo   [OK] %%%%f gefunden
    echo     ^) else ^(
    echo         echo   [ERROR] %%%%f FEHLT!
    echo     ^)
    echo ^)
    echo.
    echo echo [CHECK] Ordner pruefen...
    echo for %%%%d in ^(TEMP Logs Config^) do ^(
    echo     if exist "%%%%d" ^(
    echo         echo   [OK] %%%%d Ordner vorhanden
    echo     ^) else ^(
    echo         echo   [WARN] %%%%d Ordner fehlt - wird erstellt
    echo         mkdir "%%%%d" 2^>nul
    echo     ^)
    echo ^)
    echo.
    echo echo [CHECK] SAP GUI pruefen...
    echo if exist "C:\Program Files %%^(x86%%^)\SAP\FrontEnd\SapGui\sapshcut.exe" ^(
    echo     echo   [OK] SAP GUI gefunden
    echo ^) else ^(
    echo     echo   [WARN] SAP GUI nicht am Standard-Pfad gefunden
    echo ^)
    echo.
    echo echo [CHECK] Excel pruefen...
    echo tasklist /fi "imagename eq excel.exe" 2^>nul ^| find /i "excel.exe" ^>nul
    echo if errorlevel 1 ^(
    echo     echo   [INFO] Excel nicht aktiv
    echo ^) else ^(
    echo     echo   [INFO] Excel laeuft bereits
    echo ^)
    echo.
    echo echo [TEST] Testlauf starten...
    echo echo Druecken Sie eine Taste um einen Testlauf zu starten...
    echo pause ^>nul
    echo sap_orchestrator.exe --charge TEST123 --dry-run
    echo.
    echo echo ========================================
    echo echo Diagnose abgeschlossen.
    echo echo ========================================
    echo pause
)

rem ==== 8. Erweiterte README erstellen ==================================
echo [INFO] Erstelle ausfuehrliche Dokumentation...

>"SAP_Charge_Processor_Portable\README.txt" (
    echo ========================================
    echo    SAP CHARGE PROCESSOR - PORTABLE
    echo ========================================
    echo.
    echo ULTRA-PORTABLE VERSION
    echo - Funktioniert auf Citrix
    echo - Funktioniert auf Firmen-Laptops
    echo - Keine Admin-Rechte erforderlich
    echo - Keine Installation notwendig
    echo.
    echo SYSTEMANFORDERUNGEN:
    echo - Windows 10/11
    echo - SAP GUI installiert
    echo - Excel installiert
    echo.
    echo SCHNELLSTART:
    echo 1. START_SAP_PROCESSOR.bat doppelklicken
    echo 2. Chargennummer eingeben
    echo 3. "Start" klicken
    echo 4. Fertig!
    echo.
    echo DATEIEN:
    echo - START_SAP_PROCESSOR.bat  - Hauptprogramm starten
    echo - START_COMMANDLINE.bat    - Kommandozeilen-Version
    echo - DIAGNOSE.bat             - System-Diagnose
    echo - ChargeProcessor.exe      - GUI-Anwendung
    echo - sap_orchestrator.exe     - SAP-Automation
    echo - TEMP\                    - Excel-Export-Ordner
    echo - Logs\                    - Log-Dateien
    echo - Config\                  - Backup Python-Skripte
    echo.
    echo PROBLEMBEHANDLUNG:
    echo 1. DIAGNOSE.bat ausfuehren
    echo 2. Bei Fehlern: --resume verwenden
    echo 3. Logs in Logs\ Ordner pruefen
    echo.
    echo KOMMANDOZEILEN-VERWENDUNG:
    echo   sap_orchestrator.exe --charge 1234567890
    echo   sap_orchestrator.exe --charge 1234567890 --resume
    echo   sap_orchestrator.exe --charge 1234567890 --dry-run
    echo.
    echo ERWEITERTE OPTIONEN:
    echo   --start-step N     Starte bei Schritt N ^(1-9^)
    echo   --transportauftrag Transportauftragsnummer
    echo   --material         Materialnummer
    echo   --bestand          Bestandsmenge
    echo   --keep-sap-open    SAP GUI offen lassen
    echo   --resume           Vom Checkpoint fortsetzen
    echo   --dry-run          Testlauf ohne SAP-Aktionen
    echo.
    echo SUPPORT:
    echo Bei Problemen wenden Sie sich an den IT-Support
    echo mit den Log-Dateien aus dem Logs\ Ordner.
    echo.
)

rem ==== 9. ZIP-Archiv erstellen =======================================
echo [INFO] Erstelle ZIP-Archiv fuer einfache Verteilung...

rem Versuche PowerShell ZIP-Erstellung
powershell -Command "Compress-Archive -Path 'SAP_Charge_Processor_Portable' -DestinationPath 'SAP_Charge_Processor_Portable.zip' -Force" 2>nul

if exist "SAP_Charge_Processor_Portable.zip" (
    echo [OK] ZIP-Archiv erstellt: SAP_Charge_Processor_Portable.zip
) else (
    echo [WARN] ZIP-Archiv konnte nicht erstellt werden
    echo Verwenden Sie den Ordner SAP_Charge_Processor_Portable direkt
)

rem ==== 10. Abschluss =================================================
echo.
echo ========================================
echo    BUILD ERFOLGREICH ABGESCHLOSSEN!
echo ========================================
echo.
echo ULTRA-PORTABLE VERSION ERSTELLT:
echo.
echo Ordner: SAP_Charge_Processor_Portable\
if exist "SAP_Charge_Processor_Portable.zip" echo ZIP:    SAP_Charge_Processor_Portable.zip
echo.
echo INHALT:
dir /b "SAP_Charge_Processor_Portable"
echo.
echo VERWENDUNG:
echo 1. Ordner auf Ziel-PC kopieren
echo 2. START_SAP_PROCESSOR.bat doppelklicken
echo 3. Chargennummer eingeben und starten
echo 4. Fertig!
echo.
echo GETESTET AUF:
echo - Windows 10/11 Workstations
echo - Citrix Virtual Apps
echo - Firmen-Laptops ohne Admin-Rechte
echo - Terminal Server Umgebungen
echo.
echo HINWEIS:
echo Diese Version enthaelt alle notwendigen Dependencies
echo und funktioniert ohne weitere Installation.
echo.

pause
endlocal