@echo off
setlocal enableextensions

echo [INFO] SAP Charge Processor - Portable Build
echo.

rem ==== 1. Python-Befehl ermitteln =====================================
set "PYTHON_CMD=python"

rem Teste ob python verfuegbar ist
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python wurde nicht gefunden. Bitte installieren oder in PATH eintragen.
    pause
    exit /b 1
)

echo [INFO] Python gefunden: %PYTHON_CMD%

rem ==== 2. Verzeichnisse aufraumen ====================================
echo [INFO] Alte Build-Verzeichnisse loeschen...
for %%d in (build dist portable_app) do (
    if exist "%%d" (
        echo Loesche %%d...
        rmdir /s /q "%%d"
    )
)

rem ==== 3. Dependencies installieren ===================================
echo [INFO] PyInstaller und pywin32 installieren/aktualisieren...
%PYTHON_CMD% -m pip install --upgrade pip
%PYTHON_CMD% -m pip install --upgrade pyinstaller pywin32

rem Pruefen ob Installation erfolgreich
%PYTHON_CMD% -m pip show pyinstaller >nul 2>&1
if errorlevel 1 (
    echo [ERROR] PyInstaller Installation fehlgeschlagen
    pause
    exit /b 1
)

rem ==== 4. SAP Orchestrator bauen =====================================
echo [INFO] Baue sap_orchestrator.exe...
%PYTHON_CMD% -m PyInstaller ^
    --noconfirm ^
    --clean ^
    --onefile ^
    --console ^
    --name sap_orchestrator ^
    --hidden-import=win32com.client ^
    --hidden-import=pythoncom ^
    --hidden-import=pywintypes ^
    --collect-all pywin32 ^
    sap_orchestrator.py

if not exist "dist\sap_orchestrator.exe" (
    echo [ERROR] sap_orchestrator.exe wurde nicht erstellt
    pause
    exit /b 1
)
echo [OK] sap_orchestrator.exe erstellt

rem ==== 5. GUI bauen ==================================================
echo [INFO] Baue ChargeProcessor.exe...
%PYTHON_CMD% -m PyInstaller ^
    --noconfirm ^
    --clean ^
    --onefile ^
    --windowed ^
    --name ChargeProcessor ^
    app.py

if not exist "dist\ChargeProcessor.exe" (
    echo [ERROR] ChargeProcessor.exe wurde nicht erstellt
    pause
    exit /b 1
)
echo [OK] ChargeProcessor.exe erstellt

rem ==== 6. Portable Ordner erstellen ===================================
echo [INFO] Erstelle portable_app Ordner...
mkdir portable_app
copy /y "dist\sap_orchestrator.exe" "portable_app\"
copy /y "dist\ChargeProcessor.exe" "portable_app\"
mkdir "portable_app\TEMP"

rem ==== 7. Starter-Skripte erstellen ===================================
echo [INFO] Erstelle Starter-Skripte...

rem Normaler Starter
>"portable_app\Start.bat" (
    echo @echo off
    echo cd /d "%%~dp0"
    echo echo Starte SAP Charge Processor...
    echo start "" "ChargeProcessor.exe"
)

rem Admin-Starter
>"portable_app\Start_Admin.bat" (
    echo @echo off
    echo cd /d "%%~dp0"
    echo echo Starte als Administrator...
    echo powershell -Command "Start-Process 'ChargeProcessor.exe' -Verb RunAs -WorkingDirectory '%%cd%%'"
)

rem README erstellen
>"portable_app\README.txt" (
    echo SAP Charge Processor - Portable Version
    echo =======================================
    echo.
    echo VERWENDUNG:
    echo 1. Start.bat - Normaler Start
    echo 2. Start_Admin.bat - Als Administrator
    echo.
    echo SYSTEMANFORDERUNGEN:
    echo - Windows 10/11
    echo - SAP GUI installiert
    echo - Excel installiert
    echo.
    echo ORDNERSTRUKTUR:
    echo - ChargeProcessor.exe - Hauptprogramm
    echo - sap_orchestrator.exe - SAP Automation
    echo - TEMP\ - Fuer Excel-Exports
    echo.
)

rem ==== 8. Abschluss ==================================================
echo.
echo ========================================
echo BUILD ERFOLGREICH ABGESCHLOSSEN!
echo ========================================
echo.
echo Portable Ordner: portable_app\
echo.
echo Enthaelt:
dir /b portable_app
echo.
echo VERWENDUNG:
echo 1. portable_app Ordner kopieren
echo 2. Start.bat doppelklicken
echo 3. Fertig!
echo.

pause
endlocal