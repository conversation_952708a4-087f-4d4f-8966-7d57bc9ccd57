@echo off
setlocal enableextensions
cd /d "%~dp0"

echo ========================================
echo    SYSTEM-DIAGNOSE
echo ========================================
echo.
echo [INFO] Arbeitsverzeichnis: %cd%
echo [INFO] Datum/Zeit: %date% %time%
echo.
echo [CHECK] Dateien pruefen...
for %%f in (ChargeProcessor.exe sap_orchestrator.exe) do (
    if exist "%%f" (
        echo   [OK] %%f gefunden
    ) else (
        echo   [ERROR] %%f FEHLT!
    )
)

echo [CHECK] Ordner pruefen...
for %%d in (TEMP Logs Config) do (
    if exist "%%d" (
        echo   [OK] %%d Ordner vorhanden
    ) else (
        echo   [WARN] %%d Ordner fehlt - wird erstellt
        mkdir "%%d" 2>nul
    )
)

echo [CHECK] SAP GUI pruefen...
if exist "C:\Program Files %^(x86%^)\SAP\FrontEnd\SapGui\sapshcut.exe" (
    echo   [OK] SAP GUI gefunden
) else (
    echo   [WARN] SAP GUI nicht am Standard-Pfad gefunden
)

echo [CHECK] Excel pruefen...
tasklist /fi "imagename eq excel.exe" 2>nul | find /i "excel.exe" >nul
if errorlevel 1 (
    echo   [INFO] Excel nicht aktiv
) else (
    echo   [INFO] Excel laeuft bereits
)

echo [TEST] Testlauf starten...
echo Druecken Sie eine Taste um einen Testlauf zu starten...
pause >nul
sap_orchestrator.exe --charge TEST123 --dry-run

echo ========================================
echo Diagnose abgeschlossen.
echo ========================================
pause
