========================================
   SAP CHARGE PROCESSOR - PORTABLE
========================================

ULTRA-PORTABLE VERSION
- Funktioniert auf Citrix
- Funktioniert auf Firmen-Laptops
- Keine Admin-Rechte erforderlich
- Keine Installation notwendig

SYSTEMANFORDERUNGEN:
- Windows 10/11
- SAP GUI installiert
- Excel installiert

SCHNELLSTART:
1. START_SAP_PROCESSOR.bat doppelklicken
2. Chargennummer eingeben
3. "Start" klicken
4. Fertig!

DATEIEN:
- START_SAP_PROCESSOR.bat  - Hauptprogramm starten
- START_COMMANDLINE.bat    - Kommandozeilen-Version
- DIAGNOSE.bat             - System-Diagnose
- ChargeProcessor.exe      - GUI-Anwendung
- sap_orchestrator.exe     - SAP-Automation
- TEMP\                    - Excel-Export-Ordner
- Logs\                    - Log-Dateien
- Config\                  - Backup Python-Skripte

PROBLEMBEHANDLUNG:
1. DIAGNOSE.bat ausfuehren
2. Bei Fehlern: --resume verwenden
3. Logs in Logs\ Ordner pruefen

KOMMANDOZEILEN-VERWENDUNG:
  sap_orchestrator.exe --charge 1234567890
  sap_orchestrator.exe --charge 1234567890 --resume
  sap_orchestrator.exe --charge 1234567890 --dry-run

ERWEITERTE OPTIONEN:
  --start-step N     Starte bei Schritt N (1-9)
  --transportauftrag Transportauftragsnummer
  --material         Materialnummer
  --bestand          Bestandsmenge
  --keep-sap-open    SAP GUI offen lassen
  --resume           Vom Checkpoint fortsetzen
  --dry-run          Testlauf ohne SAP-Aktionen

SUPPORT:
Bei Problemen wenden Sie sich an den IT-Support
mit den Log-Dateien aus dem Logs\ Ordner.

