@echo off
setlocal enableextensions
cd /d "%~dp0"

echo ========================================
echo    SAP Charge Processor - PORTABLE
echo ========================================
echo.
echo [INFO] Starte SAP Charge Processor...
echo [INFO] Arbeitsverzeichnis: %cd%
echo.

rem Pruefe ob EXE existiert
if not exist "ChargeProcessor.exe" (
    echo [ERROR] ChargeProcessor.exe nicht gefunden!
    echo Bitte stellen Si<PERSON> sicher, dass alle Dateien vorhanden sind.
    pause
    exit /b 1
)

rem Erstelle TEMP-Ordner falls nicht vorhanden
if not exist "TEMP" mkdir "TEMP"
if not exist "Logs" mkdir "Logs"

rem Starte Hauptprogramm
echo [INFO] Starte GUI...
start "" "ChargeProcessor.exe"

echo [INFO] GUI gestartet. Dieses Fenster kann geschlossen werden.
timeout /t 3 /nobreak >nul
