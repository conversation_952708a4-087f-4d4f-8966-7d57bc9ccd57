import os
import sys
import subprocess
import threading
import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
from pathlib import Path
import locale
from typing import Optional

# ----------------------------
# Hintergrund-Worker-Struktur
# ----------------------------

class BackgroundProcess:
    """
    Kapselt die Hintergrundausführung eines externen Python-Skripts (später SAP).
    Übergibt die Charge als Argument.
    """

    def __init__(self, charge: str, on_progress, on_done, on_error, on_log, script_path: Optional[Path] = None):
        self.charge = charge
        self.on_progress = on_progress
        self.on_done = on_done
        self.on_error = on_error
        self.on_log = on_log
        self.script_path = script_path
        self._thread: Optional[threading.Thread] = None
        self._cancelled = threading.Event()

    def start(self):
        self._thread = threading.Thread(target=self._run, daemon=True)
        self._thread.start()

    def cancel(self):
        self._cancelled.set()

    def _run(self):
        try:
            # Arbeitsverzeichnis auf EXE-Verzeichnis setzen
            if getattr(sys, 'frozen', False):
                # Wenn als EXE ausgeführt
                exe_dir = Path(sys.executable).parent
                os.chdir(exe_dir)
                self.on_log(f"EXE-Modus: Arbeitsverzeichnis auf {exe_dir} gesetzt\n")
            
            # Portable-Build: bevorzuge EXE im gleichen Ordner
            exe_path = Path("sap_orchestrator.exe")
            
            extra = []
            if hasattr(self, "_extra_flags") and self._extra_flags:
                extra = self._extra_flags
                
            if exe_path.exists():
                cmd = [str(exe_path.absolute()), "--charge", self.charge, *extra]
                self.on_log(f"Starte: {' '.join(cmd)}\n")
            else:
                # Fallback für Development
                script_path = self.script_path or Path("sap_orchestrator.py")
                if not script_path.exists():
                    raise FileNotFoundError(f"SAP-Orchestrator wurde nicht gefunden: {script_path}")
                cmd = [sys.executable, str(script_path), "--charge", self.charge, *extra]
                self.on_log(f"Starte (Python): {' '.join(cmd)}\n")

            proc = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                encoding=locale.getpreferredencoding(False),
                errors="replace",
            )

            output_lines = []
            assert proc.stdout is not None
            for raw in proc.stdout:
                output_lines.append(raw)
                line = raw.strip()
                if line.startswith("PROGRESS "):
                    try:
                        parts = line.split(" ", 2)
                        percent = int(parts[1]) if len(parts) > 1 else 0
                        message = parts[2] if len(parts) > 2 else ""
                        self.on_progress(percent, message)
                    except Exception:
                        self.on_progress(None, "")
                else:
                    self.on_log(raw)  # Jede andere Zeile ist ein Log

                if self._cancelled.is_set():
                    proc.terminate()
                    full_output = "".join(output_lines)
                    self.on_error(RuntimeError(f"Prozess abgebrochen.\n\nBisherige Ausgabe:\n{full_output}"))
                    return

            rc = proc.wait()
            if rc == 0:
                self.on_done("Prozess erfolgreich beendet")
            else:
                full_output = "".join(output_lines)
                error_lines = [l for l in output_lines if not l.strip().startswith("PROGRESS ")]
                error_details = "".join(error_lines).strip()
                raise RuntimeError(f"SAP-Orchestrator endete mit Exit-Code {rc}.\n\nDetails:\n{error_details}")
        except Exception as e:
            self.on_error(e)


# ----------------------------
# GUI
# ----------------------------

class App(tk.Tk):
    def __init__(self):
        super().__init__()
        self.title("SAP Charge Prozess")
        self.geometry("600x500")
        self.resizable(True, True)

        # State
        self._worker: Optional[BackgroundProcess] = None
        self._indeterminate = False

        self._create_widgets()
        self._setup_layout()
        self._setup_bindings()

    def _create_widgets(self):
        # Haupt-Frame
        self.main_frame = ttk.Frame(self, padding=16)
        
        # Input Section
        self.input_frame = ttk.LabelFrame(self.main_frame, text="Eingaben", padding=12)
        
        self.label_charge = ttk.Label(self.input_frame, text="Charge:")
        self.var_charge = tk.StringVar()
        self.entry_charge = ttk.Entry(self.input_frame, textvariable=self.var_charge, width=40)

        # Optionen
        self.options_frame = ttk.LabelFrame(self.main_frame, text="Optionen", padding=12)
        
        self.var_dry_run = tk.BooleanVar(value=False)
        self.chk_dry_run = ttk.Checkbutton(self.options_frame, text="Trockenlauf (ohne SAP)", variable=self.var_dry_run)
        
        self.var_keep_open = tk.BooleanVar(value=False)
        self.chk_keep_open = ttk.Checkbutton(self.options_frame, text="SAP GUI offen lassen", variable=self.var_keep_open)

        # Buttons
        self.button_frame = ttk.Frame(self.main_frame)
        self.btn_start = ttk.Button(self.button_frame, text="Prozess starten", command=self.on_start)
        self.btn_cancel = ttk.Button(self.button_frame, text="Abbrechen", command=self.on_cancel)

        # Progress Section
        self.progress_frame = ttk.LabelFrame(self.main_frame, text="Fortschritt", padding=12)
        
        self.progress = ttk.Progressbar(self.progress_frame, orient="horizontal", mode="determinate")
        
        self.var_status = tk.StringVar(value="Bereit")
        self.label_status = ttk.Label(self.progress_frame, textvariable=self.var_status)

        # Log Section
        self.log_frame = ttk.LabelFrame(self.main_frame, text="Logs", padding=12)
        
        self.log_text = scrolledtext.ScrolledText(
            self.log_frame, 
            height=12, 
            wrap=tk.WORD,
            state=tk.DISABLED
        )
        
        self.btn_clear_log = ttk.Button(self.log_frame, text="Log löschen", command=self.clear_log)

    def _setup_layout(self):
        # Main frame
        self.main_frame.pack(fill="both", expand=True)

        # Input section
        self.input_frame.pack(fill="x", pady=(0, 8))
        self.label_charge.pack(anchor="w")
        self.entry_charge.pack(fill="x", pady=(4, 0))

        # Options section
        self.options_frame.pack(fill="x", pady=(0, 8))
        self.chk_dry_run.pack(anchor="w")
        self.chk_keep_open.pack(anchor="w", pady=(4, 0))

        # Button section
        self.button_frame.pack(fill="x", pady=(0, 8))
        self.btn_start.pack(side="left", padx=(0, 8))
        self.btn_cancel.pack(side="left")

        # Progress section
        self.progress_frame.pack(fill="x", pady=(0, 8))
        self.progress.pack(fill="x", pady=(0, 8))
        self.label_status.pack(anchor="w")

        # Log section
        self.log_frame.pack(fill="both", expand=True)
        self.log_text.pack(fill="both", expand=True, pady=(0, 8))
        self.btn_clear_log.pack(anchor="w")

    def _setup_bindings(self):
        # Enter-Taste startet Prozess
        self.bind("<Return>", lambda e: self.on_start())
        
        # Focus auf Entry-Feld
        self.entry_charge.focus_set()

    # ----------------------------
    # Ereignis-Handler
    # ----------------------------

    def on_start(self):
        charge = self.var_charge.get().strip()
        if not charge:
            messagebox.showwarning("Eingabe fehlt", "Bitte eine Charge eingeben.")
            self.entry_charge.focus_set()
            return

        # UI sperren und vorbereiten
        self._set_running_state(True)
        self._set_progress_indeterminate(False)
        self.progress["value"] = 0
        self.var_status.set("Prozess wird gestartet...")
        
        # Log löschen für neuen Start
        self.clear_log()
        self.add_log(f"=== Starte Prozess für Charge: {charge} ===\n")

        # Extra Flags sammeln
        extra_flags = []
        if self.var_dry_run.get():
            extra_flags.append("--dry-run")
        if self.var_keep_open.get():
            extra_flags.append("--keep-sap-open")

        # Worker starten
        self._worker = BackgroundProcess(
            charge=charge,
            on_progress=self._on_worker_progress,
            on_done=self._on_worker_done,
            on_error=self._on_worker_error,
            on_log=self._on_worker_log,
            script_path=Path("sap_orchestrator.py")
        )
        
        # Extra flags übergeben
        self._worker._extra_flags = extra_flags
        self._worker.start()

    def on_cancel(self):
        if self._worker:
            self._worker.cancel()
            self.var_status.set("Abbruch angefordert...")
            self.add_log("=== Abbruch angefordert ===\n")

    def clear_log(self):
        self.log_text.config(state=tk.NORMAL)
        self.log_text.delete(1.0, tk.END)
        self.log_text.config(state=tk.DISABLED)

    def add_log(self, message: str):
        """Fügt eine Log-Nachricht hinzu (Thread-sicher)"""
        def _add():
            self.log_text.config(state=tk.NORMAL)
            self.log_text.insert(tk.END, message)
            self.log_text.see(tk.END)
            self.log_text.config(state=tk.DISABLED)
        
        # Stelle sicher, dass GUI-Updates im Hauptthread passieren
        self.after(0, _add)

    # ----------------------------
    # Callbacks vom Worker
    # ----------------------------

    def _on_worker_progress(self, percent: Optional[int], message: str = ""):
        def _update():
            if percent is None:
                # Unbestimmter Fortschritt
                if not self._indeterminate:
                    self._set_progress_indeterminate(True)
                self.var_status.set("Verarbeitung läuft...")
            else:
                if self._indeterminate:
                    self._set_progress_indeterminate(False)
                self.progress["value"] = percent
                status_msg = f"Fortschritt: {percent}%"
                if message:
                    status_msg += f" - {message}"
                self.var_status.set(status_msg)
        
        self.after(0, _update)

    def _on_worker_done(self, message: str):
        def _update():
            self.progress["value"] = 100
            self.var_status.set(message)
            self.add_log(f"=== {message} ===\n")
            # Kurze Verzögerung, dann UI zurücksetzen
            self.after(1500, self._reset_ui_after_finish)
        
        self.after(0, _update)

    def _on_worker_error(self, error: Exception):
        def _update():
            self._set_running_state(False)
            self._set_progress_indeterminate(False)
            self.progress["value"] = 0
            self.var_status.set("Fehler aufgetreten")
            self.add_log(f"=== FEHLER ===\n{str(error)}\n")
            messagebox.showerror("Fehler", str(error))
        
        self.after(0, _update)

    def _on_worker_log(self, log_message: str):
        """Callback für Log-Nachrichten vom Worker"""
        self.add_log(log_message)

    # ----------------------------
    # UI-Utils
    # ----------------------------

    def _set_running_state(self, running: bool):
        state = "disabled" if running else "normal"
        self.entry_charge.configure(state=state)
        self.chk_dry_run.configure(state=state)
        self.chk_keep_open.configure(state=state)
        self.btn_start.configure(state=state)
        self.btn_cancel.configure(state="normal" if running else "disabled")

    def _set_progress_indeterminate(self, enable: bool):
        self._indeterminate = enable
        if enable:
            self.progress.configure(mode="indeterminate")
            self.progress.start(10)
        else:
            self.progress.stop()
            self.progress.configure(mode="determinate")

    def _reset_ui_after_finish(self):
        # Status meldet Erfolg, dann auf "Haupt-GUI" zurück
        self._set_running_state(False)
        self._set_progress_indeterminate(False)
        self.progress["value"] = 0
        self.var_status.set("Bereit")

        # Worker freigeben
        self._worker = None


def main():
    app = App()
    app.mainloop()


if __name__ == "__main__":
    main()