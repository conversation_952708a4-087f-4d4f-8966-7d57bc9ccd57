# encoding: utf-8
"""
sap_orchestrator – Robuste monolithische Variante
Verbesserte Fehlerbehandlung und Stabilität für SAP GUI Automation
"""
import argparse
import subprocess
import sys
import time
import json
from datetime import datetime
from pathlib import Path
from typing import Optional, Tuple

# --------------------------------------------------------------------------
# Portable Basis-Pfad
# --------------------------------------------------------------------------
def get_base_dir() -> Path:
    if getattr(sys, "frozen", False):
        return Path(sys.executable).parent
    return Path(__file__).parent


BASE_DIR = get_base_dir()
TEMP_DIR = r"H:\TEMP"
CHECKPOINT_FILE = BASE_DIR / "sap_checkpoint.json"

# --------------------------------------------------------------------------
# SAP / Programm-Konstanten
# --------------------------------------------------------------------------
SAP_EXECUTABLE_PATH = r"C:\Program Files (x86)\SAP\FrontEnd\SapGui\sapshcut.exe"
SAP_SYSTEM_ID = "TS4"
SAP_CLIENT = "009"
SAP_LANGUAGE = "DE"

LAGERORT_VON = "1090"
LAGERORT_NACH = "1010"
LAGERTYP = "957"
LAGERNUMMER_VON = "51B"
LAGERNUMMER_NACH = "512"
WERK = "5100"

EXPORT_FILE_BASE_NAME = "lsgitls24"
EXPORT_FILE_PATH = r"H:\TEMP"

CHARGEN_NR = ""

# Prozess-Schritte für Checkpoint-System
PROCESS_STEPS = {
    1: "SAP_CONNECTION",
    2: "SCRIPT1_LLSGITLS24",
    3: "EXCEL_READ",
    4: "SCRIPT2_LT15",
    5: "SCRIPT3_MIGO",
    6: "SCRIPT4_LT0651B",
    7: "SCRIPT5_LT12",
    8: "SCRIPT6_LT06512",
    9: "CLEANUP"
}

# --------------------------------------------------------------------------
# Checkpoint und Logging System
# --------------------------------------------------------------------------
class ProcessState:
    def __init__(self):
        self.current_step = 1
        self.completed_steps = []
        self.data = {}
        self.start_time = datetime.now()

    def save_checkpoint(self):
        """Speichert den aktuellen Prozessstatus"""
        try:
            checkpoint_data = {
                'current_step': self.current_step,
                'completed_steps': self.completed_steps,
                'data': self.data,
                'timestamp': self.start_time.isoformat()
            }
            with open(CHECKPOINT_FILE, 'w', encoding='utf-8') as f:
                json.dump(checkpoint_data, f, indent=2)
            log_message(f"Checkpoint gespeichert: Schritt {self.current_step}")
        except Exception as e:
            log_message(f"Warnung: Checkpoint konnte nicht gespeichert werden: {e}")

    def load_checkpoint(self) -> bool:
        """Lädt einen gespeicherten Prozessstatus"""
        try:
            if CHECKPOINT_FILE.exists():
                with open(CHECKPOINT_FILE, 'r', encoding='utf-8') as f:
                    checkpoint_data = json.load(f)
                self.current_step = checkpoint_data.get('current_step', 1)
                self.completed_steps = checkpoint_data.get('completed_steps', [])
                self.data = checkpoint_data.get('data', {})
                log_message(f"Checkpoint geladen: Fortsetzen bei Schritt {self.current_step}")
                return True
        except Exception as e:
            log_message(f"Warnung: Checkpoint konnte nicht geladen werden: {e}")
        return False

    def clear_checkpoint(self):
        """Löscht den Checkpoint nach erfolgreichem Abschluss"""
        try:
            if CHECKPOINT_FILE.exists():
                CHECKPOINT_FILE.unlink()
                log_message("Checkpoint gelöscht")
        except Exception as e:
            log_message(f"Warnung: Checkpoint konnte nicht gelöscht werden: {e}")


def generate_export_filename() -> str:
    """Generiert einen Dateinamen mit aktuellem Datum und Uhrzeit im Format: lsgitls24_DD_MM_YY_HHMMSS"""
    now = datetime.now()
    timestamp = now.strftime("%d_%m_%y_%H%M%S")
    return f"{EXPORT_FILE_BASE_NAME}_{timestamp}"


def print_progress(step: int, total: int, message: str) -> None:
    percent = int(step * 100 / max(1, total))
    print(f"PROGRESS {percent} {message}")
    sys.stdout.flush()


def log_message(msg: str) -> None:
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"[{timestamp}] {msg}")
    sys.stdout.flush()


def wait_for_element_robust(session, element_id, timeout=60, retry_count=3, description=""):
    """Robuste Wartefunktion mit mehreren Versuchen und besserer Fehlerbehandlung"""
    log_message(f"Warte auf Element: {element_id} {description}")

    for attempt in range(retry_count):
        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                # Session-Validierung vor jedem Versuch
                if not validate_session(session):
                    raise RuntimeError("SAP Session ist nicht mehr gültig")

                element = session.findById(element_id)
                if element:
                    log_message(f"Element gefunden: {element_id}")
                    return element
            except Exception as e:
                if "not found" not in str(e).lower():
                    log_message(f"Unerwarteter Fehler bei Element-Suche: {e}")
                time.sleep(0.5)

        if attempt < retry_count - 1:
            log_message(f"Versuch {attempt + 1}/{retry_count} fehlgeschlagen für {element_id}, wiederhole...")
            time.sleep(2)
            # Versuche SAP GUI zu reaktivieren
            try:
                session.findById("wnd[0]").setFocus()
            except:
                pass

    raise TimeoutError(f"Element '{element_id}' nach {retry_count} Versuchen und {timeout}s nicht gefunden")


def validate_session(session) -> bool:
    """Überprüft ob die SAP Session noch gültig ist"""
    try:
        session.findById("wnd[0]")
        return True
    except:
        return False


def safe_element_action(session, element_id, action, value=None, description="", max_retries=3):
    """Führt eine Aktion auf einem Element sicher aus mit Retry-Logik"""
    for attempt in range(max_retries):
        try:
            element = wait_for_element_robust(session, element_id, description=description)

            if action == "text":
                element.text = value
            elif action == "press":
                element.press()
            elif action == "setFocus":
                element.setFocus()
            elif action == "caretPosition":
                element.caretPosition = value
            elif action == "selected":
                element.selected = value
            elif action == "contextMenu":
                element.contextMenu()
            elif action == "selectContextMenuItem":
                element.selectContextMenuItem(value)
            else:
                raise ValueError(f"Unbekannte Aktion: {action}")

            log_message(f"Aktion '{action}' erfolgreich auf {element_id}")
            return True

        except Exception as e:
            if attempt < max_retries - 1:
                log_message(f"Aktion '{action}' fehlgeschlagen (Versuch {attempt + 1}/{max_retries}): {e}")
                time.sleep(1)
            else:
                raise RuntimeError(f"Aktion '{action}' auf Element '{element_id}' nach {max_retries} Versuchen fehlgeschlagen: {e}")

    return False


def get_sap_session_robust(max_retries=3):
    """Robuste SAP Session-Erstellung mit mehreren Versuchen"""
    try:
        import win32com.client
    except ImportError:
        raise RuntimeError("pywin32 (win32com) wird benötigt für SAP GUI automation.")

    for attempt in range(max_retries):
        try:
            # Versuche bestehende Session zu finden
            sap_gui_auto = win32com.client.GetObject("SAPGUI")
            application = sap_gui_auto.GetScriptingEngine
            if application.Children.Count > 0:
                connection = application.Children(0)
                if connection.Children.Count > 0:
                    session = connection.Children(0)
                    if validate_session(session):
                        log_message("Bestehende SAP Session gefunden und validiert.")
                        return session
        except Exception as e:
            log_message(f"Keine gültige bestehende SAP Session gefunden: {e}")

        # Starte SAP wenn nötig
        log_message(f"Starte SAP (Versuch {attempt + 1}/{max_retries})...")
        start_sap_if_needed()

        # Warte länger beim ersten Versuch
        wait_time = 10 if attempt == 0 else 5
        log_message(f"Warte {wait_time} Sekunden auf SAP GUI...")
        time.sleep(wait_time)

        try:
            sap_gui_auto = win32com.client.GetObject("SAPGUI")
            application = sap_gui_auto.GetScriptingEngine
            if application.Children.Count > 0:
                connection = application.Children(0)
                if connection.Children.Count > 0:
                    session = connection.Children(0)
                    if validate_session(session):
                        log_message("SAP Session erfolgreich erstellt und validiert.")
                        return session
        except Exception as e:
            log_message(f"SAP Session-Erstellung fehlgeschlagen (Versuch {attempt + 1}): {e}")
            if attempt < max_retries - 1:
                log_message("Warte 3 Sekunden vor erneutem Versuch...")
                time.sleep(3)

    raise RuntimeError(f"SAP GUI Session konnte nach {max_retries} Versuchen nicht erstellt werden.")


def start_sap_if_needed() -> None:
    """Startet SAP GUI falls nötig"""
    try:
        args = [
            SAP_EXECUTABLE_PATH,
            f"-system={SAP_SYSTEM_ID}",
            f"-client={SAP_CLIENT}",
            f"-language={SAP_LANGUAGE}",
            "-reuse=1",
            "-maxgui"
        ]
        subprocess.Popen(args)
        log_message("SAP GUI wird gestartet...")
    except FileNotFoundError:
        log_message("WARNUNG: sapshcut.exe nicht gefunden. Es wird angenommen, dass SAP bereits läuft.")


def close_excel() -> None:
    """Schließt Excel-Prozesse"""
    try:
        result = subprocess.run(["taskkill", "/F", "/IM", "excel.exe"],
                              check=False, capture_output=True, text=True)
        if result.returncode == 0:
            log_message("Excel-Prozesse geschlossen")
    except Exception as e:
        log_message(f"Warnung: Excel konnte nicht geschlossen werden: {e}")


def close_sap_session_safe(session) -> None:
    """Schließt SAP Session sicher"""
    if not session:
        return

    try:
        if validate_session(session):
            session.findById("wnd[0]").close()
            log_message("SAP GUI Fenster geschlossen.")
    except Exception as e:
        log_message(f"Konnte SAP GUI Fenster nicht ordnungsgemäß schließen: {e}")
        try:
            connection = session.Parent
            connection.CloseSession(session.Id)
            log_message("SAP Session geschlossen.")
        except Exception as e2:
            log_message(f"Konnte SAP Session nicht schließen: {e2}")


def delete_export_file_safe(file_path: Optional[Path]) -> None:
    """Löscht Export-Datei sicher"""
    if file_path and file_path.exists():
        try:
            file_path.unlink()
            log_message(f"Export-Datei gelöscht: {file_path}")
        except Exception as e:
            log_message(f"Warnung: Export-Datei konnte nicht gelöscht werden: {e}")


def format_sap_number(value):
    """Formatiert Zahlen für SAP (Komma statt Punkt)"""
    try:
        num = float(value)
        if num.is_integer():
            return str(int(num))
        else:
            return str(num).replace(".", ",")
    except ValueError:
        return str(value)


def find_export_file(folder: Path, base_name: str) -> Optional[Path]:
    """Findet die exportierte Excel-Datei"""
    try:
        xlsx = folder / f"{base_name}.xlsx"
        xls = folder / f"{base_name}.xls"
        if xlsx.exists():
            return xlsx
        if xls.exists():
            return xls
        # Suche nach Dateien mit Zeitstempel
        candidates = sorted(folder.glob(f"{base_name}*.*"), key=lambda p: p.stat().st_mtime, reverse=True)
        return candidates[0] if candidates else None
    except Exception as e:
        log_message(f"Fehler beim Suchen der Export-Datei: {e}")
        return None


def read_cells_from_excel(file_path: Path) -> Tuple[str, str, str]:
    """Liest Zellen aus Excel-Datei"""
    try:
        import win32com.client
    except Exception as e:
        raise RuntimeError("pywin32 (win32com) wird benötigt, um Excel-Dateien auszulesen.") from e

    excel = None
    try:
        excel = win32com.client.Dispatch("Excel.Application")
        excel.Visible = False
        excel.DisplayAlerts = False
        wb = excel.Workbooks.Open(str(file_path))
        ws = wb.ActiveSheet
        val_u2 = ws.Range("U2").Value
        val_c2 = ws.Range("C2").Value
        val_ag2 = ws.Range("AG2").Value
        wb.Close(SaveChanges=False)

        result = (str(val_u2 or "").strip(), str(val_c2 or "").strip(), str(val_ag2 or "").strip())
        log_message(f"Excel-Daten gelesen: U2={result[0]}, C2={result[1]}, AG2={result[2]}")
        return result
    except Exception as e:
        raise RuntimeError(f"Fehler beim Lesen der Excel-Datei: {e}")
    finally:
        if excel is not None:
            try:
                excel.Quit()
            except:
                pass


def execute_sap_umlagerung_process(charge_nr: str, start_step: int = 1,
                                   transportauftrag: str = "", material: str = "",
                                   bestand: str = "", keep_sap_open: bool = False) -> None:
    """
    Monolithische SAP Umlagerung Prozess-Funktion mit robuster Fehlerbehandlung
    """
    # Initialisiere Prozess-Status
    state = ProcessState()
    session = None
    export_file = None

    try:
        # Lade Checkpoint falls vorhanden
        if start_step == 1:
            state.load_checkpoint()
        else:
            state.current_step = start_step

        log_message(f"Starte SAP Umlagerung für Charge: {charge_nr}")
        log_message(f"Beginne bei Schritt: {state.current_step}")

        # Schritt 1: SAP Verbindung
        if state.current_step <= 1:
            print_progress(1, 10, "Verbinde mit SAP")
            session = get_sap_session_robust()
            state.data['session_connected'] = True
            state.current_step = 2
            state.save_checkpoint()

        # Schritt 2: Script 1 - /LSGIT/LS24 Transaction
        if state.current_step <= 2:
            print_progress(2, 10, "Führe Script 1 (/LSGIT/LS24) aus")
            if not session:
                session = get_sap_session_robust()

            log_message("Führe Script 1 Logik aus: /LSGIT/LS24 Transaction")

            # Generiere Dateinamen mit Zeitstempel
            export_filename = generate_export_filename()

            # Maximiere Fenster und starte Transaktion
            safe_element_action(session, "wnd[0]", "setFocus", description="Hauptfenster")
            session.findById("wnd[0]").maximize()

            safe_element_action(session, "wnd[0]/tbar[0]/okcd", "text", "/n/LSGIT/LS24", "Transaktionscode")
            session.findById("wnd[0]").sendVKey(0)
            time.sleep(2)  # Warte auf Transaktionsstart

            # Fülle Felder aus
            safe_element_action(session, "wnd[0]/usr/ctxtS_LGNUM-LOW", "text", LAGERNUMMER_VON, "Lagernummer von")
            safe_element_action(session, "wnd[0]/usr/ctxtS_WERKS-LOW", "text", WERK, "Werk")
            session.findById("wnd[0]").sendVKey(0)
            time.sleep(1)

            safe_element_action(session, "wnd[0]/usr/ctxtS_LGORT-LOW", "text", LAGERORT_VON, "Lagerort von")
            session.findById("wnd[0]").sendVKey(0)
            time.sleep(1)

            safe_element_action(session, "wnd[0]/usr/txtS_CHARG-LOW", "text", charge_nr, "Chargennummer")
            session.findById("wnd[0]").sendVKey(0)
            time.sleep(1)

            # Führe Suche aus
            safe_element_action(session, "wnd[0]/tbar[1]/btn[8]", "press", description="Ausführen Button")
            time.sleep(3)  # Warte auf Suchergebnisse

            # Export zu Excel
            wait_for_element_robust(session, "wnd[0]/usr/cntlALV_CONTAINER_01/shellcont/shell",
                                  description="ALV Grid")
            safe_element_action(session, "wnd[0]/usr/cntlALV_CONTAINER_01/shellcont/shell", "contextMenu")
            safe_element_action(session, "wnd[0]/usr/cntlALV_CONTAINER_01/shellcont/shell", "selectContextMenuItem", "&XXL")
            time.sleep(2)

            # Setze Dateinamen
            safe_element_action(session, "wnd[1]/usr/ssubSUB_CONFIGURATION:SAPLSALV_GUI_CUL_EXPORT_AS:0512/txtGS_EXPORT-FILE_NAME",
                              "text", export_filename, "Export Dateiname")
            safe_element_action(session, "wnd[1]/tbar[0]/btn[20]", "press", description="Weiter Button")
            time.sleep(2)

            # Setze Pfad und speichere
            safe_element_action(session, "wnd[1]/usr/ctxtDY_PATH", "text", EXPORT_FILE_PATH, "Export Pfad")
            safe_element_action(session, "wnd[1]/tbar[0]/btn[0]", "press", description="Speichern Button")
            time.sleep(5)  # Warte auf Export

            state.data['export_filename'] = export_filename
            state.current_step = 3
            state.save_checkpoint()
            log_message("Script 1 erfolgreich abgeschlossen")

        # Schritt 3: Excel lesen
        if state.current_step <= 3:
            print_progress(3, 10, "Lese Export-Excel")
            close_excel()  # Schließe Excel vor dem Lesen

            export_filename = state.data.get('export_filename', '')
            if not export_filename and not transportauftrag:
                raise ValueError("Export-Dateiname fehlt und keine Transportauftragsnummer angegeben")

            if export_filename:
                export_dir = Path(TEMP_DIR)
                export_file = find_export_file(export_dir, export_filename)
                if not export_file:
                    raise FileNotFoundError(f"Export-Datei nicht gefunden in {TEMP_DIR}")

                transportauftrag_nr, material_nr, gesamtbestand = read_cells_from_excel(export_file)
                state.data.update({
                    'transportauftrag_nr': transportauftrag_nr,
                    'material_nr': material_nr,
                    'gesamtbestand': gesamtbestand,
                    'export_file_path': str(export_file)
                })
            else:
                # Verwende übergebene Parameter
                state.data.update({
                    'transportauftrag_nr': transportauftrag,
                    'material_nr': material,
                    'gesamtbestand': bestand
                })

            state.current_step = 4
            state.save_checkpoint()
            log_message(f"Excel-Daten: TA={state.data['transportauftrag_nr']}, Material={state.data['material_nr']}, Bestand={state.data['gesamtbestand']}")

        # Schritt 4: Script 2 - LT15 Transaction
        if state.current_step <= 4:
            print_progress(4, 10, "Führe Script 2 (LT15) aus")
            if not session:
                session = get_sap_session_robust()

            transportauftrag_nr = state.data.get('transportauftrag_nr', '')
            if not transportauftrag_nr:
                raise ValueError("Transportauftragsnummer fehlt")

            log_message("Führe Script 2 Logik aus: LT15 Transaction")

            safe_element_action(session, "wnd[0]/tbar[0]/okcd", "text", "/nLT15", "LT15 Transaktion")
            session.findById("wnd[0]").sendVKey(0)
            time.sleep(2)

            safe_element_action(session, "wnd[0]/usr/txtLTAK-TANUM", "text", transportauftrag_nr, "Transportauftragsnummer")
            session.findById("wnd[0]").sendVKey(0)
            time.sleep(2)

            safe_element_action(session, "wnd[0]/usr/txtLTAK-TANUM", "caretPosition", 4)
            session.findById("wnd[0]").sendVKey(0)
            safe_element_action(session, "wnd[0]/tbar[0]/btn[11]", "press", description="Ausführen Button")
            time.sleep(2)

            state.current_step = 5
            state.save_checkpoint()
            log_message("Script 2 erfolgreich abgeschlossen")

        # Schritt 5: Script 3 - MIGO Transaction
        if state.current_step <= 5:
            print_progress(5, 10, "Führe Script 3 (MIGO) aus")
            if not session:
                session = get_sap_session_robust()

            material_nr = state.data.get('material_nr', '')
            gesamtbestand = state.data.get('gesamtbestand', '')
            if not material_nr or not gesamtbestand:
                raise ValueError("Material oder Bestand fehlt")

            log_message("Führe Script 3 Logik aus: MIGO Transaction")

            safe_element_action(session, "wnd[0]/tbar[0]/okcd", "text", "/nMIGO", "MIGO Transaktion")
            session.findById("wnd[0]").sendVKey(0)
            time.sleep(2)

            # Fülle MIGO Felder aus
            safe_element_action(session, "wnd[0]/usr/ssubSUB_MAIN_CARRIER:SAPLMIGO:0006/subSUB_ITEMLIST:SAPLMIGO:0200/tblSAPLMIGOTV_GOITEM/ctxtGOITEM-MAKTX[1,0]",
                              "text", material_nr, "Material")

            bestand_formatiert = format_sap_number(gesamtbestand)
            safe_element_action(session, "wnd[0]/usr/ssubSUB_MAIN_CARRIER:SAPLMIGO:0006/subSUB_ITEMLIST:SAPLMIGO:0200/tblSAPLMIGOTV_GOITEM/txtGOITEM-ERFMG[4,0]",
                              "text", bestand_formatiert, "Bestand")

            safe_element_action(session, "wnd[0]/usr/ssubSUB_MAIN_CARRIER:SAPLMIGO:0006/subSUB_ITEMLIST:SAPLMIGO:0200/tblSAPLMIGOTV_GOITEM/ctxtGOITEM-LGOBE[6,0]",
                              "text", LAGERORT_VON, "Lagerort von")
            safe_element_action(session, "wnd[0]/usr/ssubSUB_MAIN_CARRIER:SAPLMIGO:0006/subSUB_ITEMLIST:SAPLMIGO:0200/tblSAPLMIGOTV_GOITEM/ctxtGOITEM-NAME1[13,0]",
                              "text", WERK, "Werk")

            session.findById("wnd[0]").sendVKey(0)
            time.sleep(1)

            safe_element_action(session, "wnd[0]/usr/ssubSUB_MAIN_CARRIER:SAPLMIGO:0006/subSUB_ITEMLIST:SAPLMIGO:0200/tblSAPLMIGOTV_GOITEM/ctxtGOITEM-CHARG[10,0]",
                              "text", charge_nr, "Charge")
            safe_element_action(session, "wnd[0]/usr/ssubSUB_MAIN_CARRIER:SAPLMIGO:0006/subSUB_ITEMLIST:SAPLMIGO:0200/tblSAPLMIGOTV_GOITEM/ctxtGOITEM-UMLGOBE[32,0]",
                              "text", LAGERORT_NACH, "Lagerort nach")
            safe_element_action(session, "wnd[0]/usr/ssubSUB_MAIN_CARRIER:SAPLMIGO:0006/subSUB_ITEMLIST:SAPLMIGO:0200/tblSAPLMIGOTV_GOITEM/ctxtGOITEM-UMCHA[36,0]",
                              "text", charge_nr, "Charge nach")

            time.sleep(1)

            safe_element_action(session, "wnd[0]/usr/ssubSUB_MAIN_CARRIER:SAPLMIGO:0006/subSUB_ITEMLIST:SAPLMIGO:0200/tblSAPLMIGOTV_GOITEM/ctxtGOITEM-UMCHA[36,0]",
                              "setFocus")
            safe_element_action(session, "wnd[0]/usr/ssubSUB_MAIN_CARRIER:SAPLMIGO:0006/subSUB_ITEMLIST:SAPLMIGO:0200/tblSAPLMIGOTV_GOITEM/ctxtGOITEM-UMCHA[36,0]",
                              "caretPosition", 10)
            session.findById("wnd[0]").sendVKey(0)
            time.sleep(2)

            # Speichern
            safe_element_action(session, "wnd[0]/tbar[1]/btn[7]", "press", description="Prüfen Button")
            time.sleep(1)
            safe_element_action(session, "wnd[1]/tbar[0]/btn[0]", "press", description="Bestätigen Button")
            time.sleep(1)
            safe_element_action(session, "wnd[0]/tbar[1]/btn[23]", "press", description="Buchen Button")
            time.sleep(2)

            state.current_step = 6
            state.save_checkpoint()
            log_message("Script 3 erfolgreich abgeschlossen")

        # Schritt 6: Script 4 - LT06 Transaction (51B)
        if state.current_step <= 6:
            print_progress(6, 10, "Führe Script 4 (LT06 51B) aus")
            if not session:
                session = get_sap_session_robust()

            log_message("Führe Script 4 Logik aus: LT06 Transaction")

            safe_element_action(session, "wnd[0]/tbar[0]/okcd", "text", "/nLT06", "LT06 Transaktion")
            session.findById("wnd[0]").sendVKey(0)
            time.sleep(2)

            session.findById("wnd[0]").sendVKey(0)
            time.sleep(1)

            safe_element_action(session, "wnd[0]/tbar[1]/btn[6]", "press", description="Weitere Optionen")
            time.sleep(1)

            safe_element_action(session, "wnd[0]/usr/ctxtLTAP-VLTYP", "text", LAGERTYP, "Lagertyp")
            time.sleep(1)
            safe_element_action(session, "wnd[0]/usr/ctxtLTAP-VLTYP", "setFocus")
            time.sleep(1)
            safe_element_action(session, "wnd[0]/usr/ctxtLTAP-VLTYP", "caretPosition", 3)
            time.sleep(1)
            session.findById("wnd[0]").sendVKey(0)
            time.sleep(1)

            safe_element_action(session, "wnd[0]/tbar[0]/btn[11]", "press", description="Ausführen Button")
            time.sleep(2)

            try:
                session.findById("wnd[0]/sbar").doubleClick()
                time.sleep(1)
                session.findById("wnd[0]/shellcont").close()
                time.sleep(2)
            except:
                log_message("Warnung: Konnte Statusbar/Shell nicht schließen")

            state.current_step = 7
            state.save_checkpoint()
            log_message("Script 4 erfolgreich abgeschlossen")

        # Schritt 7: Script 5 - LT12 Transaction
        if state.current_step <= 7:
            print_progress(7, 10, "Führe Script 5 (LT12) aus")
            if not session:
                session = get_sap_session_robust()

            log_message("Führe Script 5 Logik aus: LT12 Transaction")

            safe_element_action(session, "wnd[0]/tbar[0]/okcd", "text", "/nLT12", "LT12 Transaktion")
            session.findById("wnd[0]").sendVKey(0)
            time.sleep(2)

            safe_element_action(session, "wnd[0]/usr/chkRL03T-OFPOS", "selected", True, "Offene Positionen")
            time.sleep(1)
            safe_element_action(session, "wnd[0]/usr/chkRLIST-SUBST", "selected", True, "Substitution")
            time.sleep(1)

            session.findById("wnd[0]").sendVKey(0)
            safe_element_action(session, "wnd[0]/tbar[0]/btn[11]", "press", description="Ausführen Button")
            time.sleep(2)

            state.current_step = 8
            state.save_checkpoint()
            log_message("Script 5 erfolgreich abgeschlossen")

        # Schritt 8: Script 6 - LT06 Transaction (512)
        if state.current_step <= 8:
            print_progress(8, 10, "Führe Script 6 (LT06 512) aus")
            if not session:
                session = get_sap_session_robust()

            log_message("Führe Script 6 Logik aus: LT06 Transaction (Lagernummer nach)")

            safe_element_action(session, "wnd[0]/tbar[0]/okcd", "text", "/nLT06", "LT06 Transaktion")
            session.findById("wnd[0]").sendVKey(0)
            time.sleep(2)

            safe_element_action(session, "wnd[0]/usr/ctxtRL02B-LGNUM", "text", LAGERNUMMER_NACH, "Lagernummer nach")
            time.sleep(1)
            safe_element_action(session, "wnd[0]/usr/ctxtRL02B-LGNUM", "setFocus")
            time.sleep(1)
            safe_element_action(session, "wnd[0]/usr/ctxtRL02B-LGNUM", "caretPosition", 3)
            time.sleep(1)
            session.findById("wnd[0]").sendVKey(0)
            time.sleep(1)

            safe_element_action(session, "wnd[0]/tbar[1]/btn[6]", "press", description="Weitere Optionen")
            time.sleep(1)

            safe_element_action(session, "wnd[0]/tbar[0]/btn[11]", "press", description="Ausführen Button")
            time.sleep(1)

            state.current_step = 9
            state.save_checkpoint()
            log_message("Script 6 erfolgreich abgeschlossen")

        # Schritt 9: Cleanup
        if state.current_step <= 9:
            print_progress(9, 10, "Räume auf (Excel/SAP)")
            close_excel()

            # Excel-Datei löschen
            if 'export_file_path' in state.data:
                export_file = Path(state.data['export_file_path'])
                delete_export_file_safe(export_file)

            # SAP GUI Fenster schließen (nur wenn nicht explizit offen gehalten werden soll)
            if not keep_sap_open and session:
                close_sap_session_safe(session)

            state.clear_checkpoint()
            print_progress(10, 10, "Fertig")
            log_message("SAP Umlagerung erfolgreich abgeschlossen")

    except Exception as e:
        log_message(f"Fehler in Schritt {state.current_step}: {e}")
        state.save_checkpoint()  # Speichere Status für Wiederaufnahme

        # Cleanup bei Fehler
        try:
            close_excel()
            if session and not keep_sap_open:
                close_sap_session_safe(session)
        except:
            pass

        raise RuntimeError(f"SAP Umlagerung fehlgeschlagen in Schritt {state.current_step}: {e}")


def main() -> None:
    """Neue Hauptfunktion mit robuster monolithischer Implementierung"""
    parser = argparse.ArgumentParser(description="SAP Orchestrator - Robuste Version")
    parser.add_argument("--charge", required=True, help="Chargennummer")
    parser.add_argument("--dry-run", action="store_true", help="Trockenlauf ohne SAP-Aktionen")
    parser.add_argument("--start-step", type=int, default=1, choices=range(1, 10),
                       help="Startet bei angegebenem Schritt (1-9)")
    parser.add_argument("--transportauftrag", help="Transportauftragsnummer (falls bekannt)")
    parser.add_argument("--material", help="Materialnummer (falls bekannt)")
    parser.add_argument("--bestand", help="Gesamtbestand (falls bekannt)")
    parser.add_argument("--keep-sap-open", action="store_true", help="SAP GUI offen lassen")
    parser.add_argument("--resume", action="store_true", help="Fortsetzen vom letzten Checkpoint")
    args = parser.parse_args()

    global CHARGEN_NR
    CHARGEN_NR = args.charge

    log_message(f"=== SAP Orchestrator gestartet ===")
    log_message(f"Charge: {CHARGEN_NR}")
    log_message(f"Start-Schritt: {args.start_step}")

    if args.dry_run:
        log_message("=== TROCKENLAUF MODUS ===")
        print_progress(10, 10, "Trockenlauf – beendet")
        return

    try:
        # Bestimme Start-Schritt
        start_step = 1
        if args.resume:
            # Versuche Checkpoint zu laden
            temp_state = ProcessState()
            if temp_state.load_checkpoint():
                start_step = temp_state.current_step
                log_message(f"Fortsetzen vom Checkpoint bei Schritt {start_step}")
            else:
                log_message("Kein Checkpoint gefunden, starte von Anfang")
        else:
            start_step = args.start_step

        # Führe monolithischen Prozess aus
        execute_sap_umlagerung_process(
            charge_nr=CHARGEN_NR,
            start_step=start_step,
            transportauftrag=args.transportauftrag or "",
            material=args.material or "",
            bestand=args.bestand or "",
            keep_sap_open=args.keep_sap_open
        )

        log_message("=== SAP Orchestrator erfolgreich beendet ===")

    except KeyboardInterrupt:
        log_message("=== Prozess durch Benutzer abgebrochen ===")
        print_progress(0, 10, "Abgebrochen")
        sys.exit(1)

    except Exception as e:
        log_message(f"=== FEHLER: {e} ===")
        log_message("Checkpoint wurde gespeichert. Verwende --resume zum Fortsetzen.")
        print_progress(0, 10, f"Fehler: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()